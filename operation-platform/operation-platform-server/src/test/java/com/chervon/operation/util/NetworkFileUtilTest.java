package com.chervon.operation.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * NetworkFileUtil 单元测试类
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@DisplayName("网络文件工具类单元测试")
class NetworkFileUtilTest {

    // 测试用的URL常量
    private static final String VALID_URL = "https://httpbin.org/status/200";
    private static final String NOT_FOUND_URL = "https://httpbin.org/status/404";
    private static final String INVALID_URL = "invalid-url";
    private static final String EMPTY_URL = "";
    private static final String NULL_URL = null;
    
    // 测试用的文件URL
    private static final String PDF_URL = "https://example.com/test/file.pdf";
    private static final String FILE_WITH_PARAMS_URL = "https://example.com/test/document.pdf?version=1&type=manual";
    private static final String URL_WITHOUT_FILE = "https://example.com/path/";

    @Nested
    @DisplayName("文件下载功能测试")
    class DownloadFileTests {

        @Test
        @DisplayName("应该正确调用下载方法")
        void shouldCallDownloadMethod() throws IOException {
            // Given
            Path tempFile = Files.createTempFile("test", ".txt");
            File destFile = tempFile.toFile();
            
            try {
                // When - 测试方法调用不抛出异常
                assertDoesNotThrow(() -> {
                    long size = NetworkFileUtil.downloadFile(PDF_URL, destFile);
                    // 验证返回值是数字类型
                    assertTrue(size >= 0 || size == -1, "下载方法应该返回有效的大小值");
                });
                
            } finally {
                // 清理临时文件
                Files.deleteIfExists(tempFile);
            }
        }

        @Test
        @DisplayName("应该处理无效URL")
        void shouldHandleInvalidUrl() throws IOException {
            // Given
            Path tempFile = Files.createTempFile("test", ".txt");
            File destFile = tempFile.toFile();
            
            try {
                // When & Then - 无效URL应该能正常处理
                assertDoesNotThrow(() -> {
                    NetworkFileUtil.downloadFile(INVALID_URL, destFile);
                });
                
            } finally {
                Files.deleteIfExists(tempFile);
            }
        }

        @Test
        @DisplayName("应该处理null参数")
        void shouldHandleNullParameters() throws IOException {
            // Given
            Path tempFile = Files.createTempFile("test", ".txt");
            File destFile = tempFile.toFile();
            
            try {
                // When & Then - null参数应该能正常处理
                assertDoesNotThrow(() -> {
                    NetworkFileUtil.downloadFile(NULL_URL, destFile);
                });
                
            } finally {
                Files.deleteIfExists(tempFile);
            }
        }
    }

    @Nested
    @DisplayName("文件名获取功能测试")
    class GetFileNameTests {

        @Test
        @DisplayName("应该正确获取PDF文件名")
        void shouldGetPdfFileName() {
            // When
            String fileName = NetworkFileUtil.getFileName(PDF_URL);
            
            // Then
            assertEquals("file.pdf", fileName, "应该正确提取PDF文件名");
        }

        @Test
        @DisplayName("应该处理带参数的URL")
        void shouldHandleUrlWithParameters() {
            // When
            String fileName = NetworkFileUtil.getFileName(FILE_WITH_PARAMS_URL);
            
            // Then
            assertEquals("document.pdf", fileName, "应该忽略URL参数，正确提取文件名");
        }

        @Test
        @DisplayName("应该处理不包含文件名的URL")
        void shouldHandleUrlWithoutFileName() {
            // When
            String fileName = NetworkFileUtil.getFileName(URL_WITHOUT_FILE);
            
            // Then
            assertEquals("", fileName, "不包含文件名的URL应该返回空字符串");
        }

        @Test
        @DisplayName("应该处理空URL")
        void shouldHandleEmptyUrl() {
            // When
            String fileName = NetworkFileUtil.getFileName(EMPTY_URL);
            
            // Then
            assertEquals("", fileName, "空URL应该返回空字符串");
        }

        @Test
        @DisplayName("应该处理null参数")
        void shouldHandleNullUrl() {
            // When & Then
            assertDoesNotThrow(() -> {
                String fileName = NetworkFileUtil.getFileName(NULL_URL);
                // null参数可能返回null或空字符串，都是可接受的
                assertTrue(fileName == null || fileName.isEmpty(), "null URL应该返回null或空字符串");
            });
        }

        @Test
        @DisplayName("应该处理无效URL")
        void shouldHandleInvalidUrl() {
            // When & Then - 无效URL可能抛出异常或返回特定值
            assertDoesNotThrow(() -> {
                NetworkFileUtil.getFileName(INVALID_URL);
            });
        }
    }

    @Nested
    @DisplayName("文件存在性检查功能测试")
    class CheckFileExistTests {

        @Test
        @DisplayName("应该检查文件存在性")
        void shouldCheckFileExistence() {
            // When & Then - 测试方法能正常执行
            assertDoesNotThrow(() -> {
                boolean exists = NetworkFileUtil.checkFileExist(VALID_URL);
                // 返回值应该是boolean类型
                assertTrue(exists == true || exists == false, "应该返回boolean值");
            });
        }

        @Test
        @DisplayName("应该处理404状态")
        void shouldHandle404Status() {
            // When
            boolean exists = NetworkFileUtil.checkFileExist(NOT_FOUND_URL);
            
            // Then
            assertFalse(exists, "404状态应该返回false");
        }

        @Test
        @DisplayName("应该处理无效URL")
        void shouldHandleInvalidUrlForExistCheck() {
            // When
            boolean exists = NetworkFileUtil.checkFileExist(INVALID_URL);
            
            // Then
            assertFalse(exists, "无效URL应该返回false");
        }

        @Test
        @DisplayName("应该处理null参数")
        void shouldHandleNullUrlForExistCheck() {
            // When
            boolean exists = NetworkFileUtil.checkFileExist(NULL_URL);
            
            // Then
            assertFalse(exists, "null URL应该返回false");
        }

        @Test
        @DisplayName("应该处理空字符串参数")
        void shouldHandleEmptyUrlForExistCheck() {
            // When
            boolean exists = NetworkFileUtil.checkFileExist(EMPTY_URL);
            
            // Then
            assertFalse(exists, "空URL应该返回false");
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("应该处理特殊字符URL")
        void shouldHandleSpecialCharacterUrl() {
            // Given
            String specialUrl = "https://example.com/测试文件.pdf";
            
            // When & Then
            assertDoesNotThrow(() -> {
                String fileName = NetworkFileUtil.getFileName(specialUrl);
                assertNotNull(fileName, "特殊字符URL应该能正常处理");
            });
        }

        @Test
        @DisplayName("应该处理长URL")
        void shouldHandleLongUrl() {
            // Given
            StringBuilder longUrlBuilder = new StringBuilder("https://example.com/");
            for (int i = 0; i < 50; i++) {
                longUrlBuilder.append("path").append(i).append("/");
            }
            longUrlBuilder.append("file.pdf");
            String longUrl = longUrlBuilder.toString();
            
            // When & Then
            assertDoesNotThrow(() -> {
                String fileName = NetworkFileUtil.getFileName(longUrl);
                assertEquals("file.pdf", fileName, "长URL应该能正确提取文件名");
            });
        }

        @Test
        @DisplayName("应该处理各种文件扩展名")
        void shouldHandleDifferentFileExtensions() {
            // Given
            String[] urls = {
                "https://example.com/document.pdf",
                "https://example.com/image.jpg",
                "https://example.com/data.json",
                "https://example.com/script.js",
                "https://example.com/style.css"
            };
            
            String[] expectedNames = {"document.pdf", "image.jpg", "data.json", "script.js", "style.css"};
            
            // When & Then
            for (int i = 0; i < urls.length; i++) {
                String fileName = NetworkFileUtil.getFileName(urls[i]);
                assertEquals(expectedNames[i], fileName, "应该正确提取" + expectedNames[i] + "文件名");
            }
        }
    }
}
