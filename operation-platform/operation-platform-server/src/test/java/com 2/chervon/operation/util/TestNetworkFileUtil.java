package com.chervon.operation.util;


import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.oss.uitl.S3Util;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;

@SpringBootTest
public class TestNetworkFileUtil {

    @Autowired
    private S3Util s3Util;

    @Autowired
    private AwsProperties awsProperties;
    @Test
    public  void testCheckNetworkFileExist() {
        String networkFileUrl = "https://do05icstb9a1p.cloudfront.net/picture%2Fba1400t-ba2240t-ba2242t-ba2800t-ba3360t-ba4200t-ba4480t-ba5600t-ba6720t_ego_battery_23-0207_manual.pdf"; // 网络文件的URL
        assert  NetworkFileUtil.checkFileExist(networkFileUrl);
    }

    @Test
    public void testGetNameFromNetworkFile(){
        String networkFileUrl = "https://do05icstb9a1p.cloudfront.net/picture%2Fba1400t-ba2240t-ba2242t-ba2800t-ba3360t-ba4200t-ba4480t-ba5600t-ba6720t_ego_battery_23-0207_manual.pdf"; // 网络文件的URL
        System.out.println(NetworkFileUtil.getFileName(networkFileUrl));
    }


    @Test
    public void testDownloadNetworkFile() {
        String networkFileUrl = "https://do05icstb9a1p.cloudfront.net/picture%2Fba1400t-ba2240t-ba2242t-ba2800t-ba3360t-ba4200t-ba4480t-ba5600t-ba6720t_ego_battery_23-0207_manual.pdf"; // 网络文件的URL
        File destDir=new File("D:\\networkfile");
        if(!destDir.exists()){
            destDir.mkdirs();
        }
        String fileName = destDir+File.separator+System.currentTimeMillis()+"_"+ NetworkFileUtil.getFileName(networkFileUrl);
        File destFile = new File(fileName);
        NetworkFileUtil.downloadFile(networkFileUrl,destFile);
    }

    @Test
    public void testDownloadNetworkFileAndTransferToS3() {
            String networkFileUrl = "https://do05icstb9a1p.cloudfront.net/picture%2Fba1400t-ba2240t-ba2242t-ba2800t-ba3360t-ba4200t-ba4480t-ba5600t-ba6720t_ego_battery_23-0207_manual.pdf"; // 网络文件的URL
            File destDir=new File("D:\\networkfile");
            if(!destDir.exists()){
                destDir.mkdirs();
            }
            String destFileName =  NetworkFileUtil.getFileName(networkFileUrl)+"_"+System.currentTimeMillis();
            File destFile = new File(destDir+File.separator+destFileName);
            NetworkFileUtil.downloadFile(networkFileUrl,destFile);
            s3Util.uploadFile(awsProperties.getPictureBucket().getName(), "networkfile/" + destFileName, destFile, true);
            destFile.delete();
    }

}
