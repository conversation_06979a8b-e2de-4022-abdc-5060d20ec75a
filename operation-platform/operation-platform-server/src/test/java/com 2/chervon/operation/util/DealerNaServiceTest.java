package com.chervon.operation.util;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.operation.api.vo.AppDealerVo;
import com.chervon.operation.service.DealerNaService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/8 11:03
 * @desc 描述
 */
@SpringBootTest
public class DealerNaServiceTest {

    @Autowired
    private DealerNaService dealerNaService;

    @Test
    public void getDealerNaPage(){
//        PageResult<AppDealerVo> pageResult1 = dealerNaService.page("All", 41.8247116, -71.3455646, 10000, 1, 6);
//        PageResult<AppDealerVo> pageResult2 = dealerNaService.page("All", 41.8247116, -71.3455646, 10000, 2, 6);
//        PageResult<AppDealerVo> pageResult3 = dealerNaService.page("All", 41.8247116, -71.3455646, 10000, 7, 6);
//        PageResult<AppDealerVo> pageResult4 = dealerNaService.page("All", 41.8247116, -71.3455646, 10000, 8, 6);
//        System.out.println(pageResult1);
//        System.out.println(pageResult2);
//        System.out.println(pageResult3);
//        System.out.println(pageResult4);
    }

    @Test
    public void getDealerNaList(){
        List<AppDealerVo> pageResult = dealerNaService.list("All", 41.8247116, -71.3455646, 41.679988741921996, 41.969434458078005, -71.539774383818, -71.151354816182, false);
        System.out.println(JsonUtils.toJson(pageResult));
    }


}
