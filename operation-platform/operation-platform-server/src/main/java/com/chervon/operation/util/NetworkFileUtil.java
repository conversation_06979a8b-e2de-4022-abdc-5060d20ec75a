package com.chervon.operation.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @Author：flynn.wang
 * @Date：2024/4/7 11:01
 */
@Slf4j
public class NetworkFileUtil {

    /**
     * 下载网络文件
     * @param networkFileUrl 网络文件URL
     * @param destFile 目标文件
     * @return 下载的文件大小，失败时返回-1
     */
    public static long downloadFile(String networkFileUrl, File destFile){
        // 参数验证
        if (StrUtil.isBlank(networkFileUrl)) {
            log.warn("网络文件URL为空，无法下载");
            return -1;
        }

        if (destFile == null) {
            log.warn("目标文件为空，无法下载");
            return -1;
        }

        try {
            return HttpUtil.downloadFile(networkFileUrl, destFile);
        } catch (Exception e) {
            log.error("下载网络文件失败，URL: {}, 错误: {}", networkFileUrl, e.getMessage());
            return -1;
        }
    }

    /**
     * 获取网络文件文件名
     * @param networkFileUrl 网络文件URL
     * @return 文件名，如果无法获取则返回空字符串
     */
    public static String getFileName(String networkFileUrl){
        // 参数验证
        if (StrUtil.isBlank(networkFileUrl)) {
            return "";
        }

        try {
            String path = URLUtil.getPath(networkFileUrl);
            if (StrUtil.isBlank(path)) {
                return "";
            }

            // 如果路径以斜杠结尾，说明是目录而不是文件
            if (path.endsWith("/")) {
                return "";
            }

            String fileName = FileUtil.getName(path);
            // 如果文件名为空或者只包含空白字符，返回空字符串
            return StrUtil.isBlank(fileName) ? "" : fileName;
        } catch (Exception e) {
            log.error("获取文件名失败，URL: {}, 错误: {}", networkFileUrl, e.getMessage());
            return "";
        }
    }

    /**
     * 检查网络文件是否存在
     *
     * @param networkFileUrl 网络文件URL
     * @return true-存在，false-不存在
     */
    public static boolean checkFileExist(String networkFileUrl) {
        // 参数验证
        if (StrUtil.isBlank(networkFileUrl)) {
            return false;
        }

        HttpURLConnection connection = null;
        try {
            URL url = new URL(networkFileUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);
            connection.setRequestMethod("HEAD");
            int responseCode = connection.getResponseCode();
            return responseCode == HttpURLConnection.HTTP_OK;
        } catch (Exception e) {
            log.error("检查网络文件存在性失败，URL: {}, 错误: {}", networkFileUrl, e.getMessage());
            return false;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}
