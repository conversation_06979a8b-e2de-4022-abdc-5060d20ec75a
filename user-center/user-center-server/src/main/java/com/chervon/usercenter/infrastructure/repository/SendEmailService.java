package com.chervon.usercenter.infrastructure.repository;

import cn.hutool.core.io.resource.ClassPathResource;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.amazonaws.services.simpleemail.model.*;
import com.chervon.common.core.constant.StringPool;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.mail.MailUtils;
import com.chervon.common.core.utils.file.FileUtils;
import com.chervon.common.i18n.config.MessageConfig;
import com.chervon.usercenter.api.dto.InviteRegisterDto;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.MessagingException;
import javax.mail.Part;
import javax.mail.Session;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SendEmailService {
    private static final String FROM_DEFAULT = "<EMAIL>";
    private static final Pattern EMAIL_TITLE = Pattern.compile("(?<=<title>).*(?=</title>)");
    @Value("${mail.ses.region}")
    private String region;
    @Value("${mail.ses.accessId}")
    private String accessId;
    @Value("${mail.ses.secretKey}")
    private String secretKey;
    @Value("${mail.template-name}")
    private String templateName;
    @Value("${mail.template-name-reset}")
    private String templateNameReset;
    @Value("${mail.template-name-invite}")
    private String templateNameInvite;
    @Value("${mail.from}")
    private String sendFrom;
    @Value("${mail.img-path}")
    private String imgPath;

    /** 邮件模板文件路径 */
    private final String path;

    /** 附件图片名称 */
    private static final String ATTACH_PNG = "attach.png";

    public SendEmailService(MessageConfig messageConfig) {
        if (messageConfig == null) {
            throw new ServiceException("MessageConfig cannot be null");
        }
        this.path = System.getProperty("user.dir")
                + File.separator
                + messageConfig.getBaseFolder()
                + File.separator;
    }

    private String getFrom() {
        if (StringUtils.isBlank(sendFrom) || !sendFrom.contains("@")) {
            return FROM_DEFAULT;
        }
        return sendFrom;
    }

    private AmazonSimpleEmailService getSendEmailClient() {
        AWSCredentials credentials = new BasicAWSCredentials(accessId, secretKey);
        return AmazonSimpleEmailServiceClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withRegion(region)
                .build();
    }

    /**
     * 描述：发送带附件的邮件
     * @date 2024/6/17 17:53
     * @param code 二维码，sendTo 发送给谁，language 语言，op 操作类型
     **/
    @Async
    public void sendCode(String code, String sendTo, String language, String op) {
        log.info("Async send code, Thread: {}", Thread.currentThread().getName());
        String mailTemplate;
        if (op.equals(UserCenterConstant.FORGET_PASSWORD)) {
            mailTemplate = templateNameReset;
        } else {
            mailTemplate = templateName;
        }
        try {
            String fileName = getMailFileName(mailTemplate, language);
            HashMap<String, Object> map = new HashMap<>(2);
            map.put(UserCenterConstant.USER_NAME, sendTo);
            map.put(UserCenterConstant.CODE, code);
            File file = new File(path + fileName);
            if (!file.exists()) {
                fileName = getMailFileName(mailTemplate, "en");
            }
            String fileContent = MailUtils.getTemplateMailText(path, fileName, map);
            sendEmail(fileContent, sendTo);
        } catch (Exception ex) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SEND_MAIL_ERROR, ex.getMessage());
        }
    }

    /**
     * 描述：获取邮件模板文件路径
     * @param templateName 邮件模板名称
     * @param language 语种
     */
    public String getMailFileName(String templateName, String language) {
        String[] split = templateName.split("\\.");
        return split[0] + StringPool.UNDERSCORE + language + StringPool.DOT + split[1];
    }

    @Async
    public void sendInviteRegister(InviteRegisterDto dto) {
        log.info("Async send invite, Thread: {}", Thread.currentThread().getName());
        try {
            String language = dto.getLanguage();
            String fileName = getMailFileName(templateNameInvite, language);
            Map<String, Object> map = new HashMap<>(3);
            map.put(UserCenterConstant.FIRST_NAME, Optional.ofNullable(dto.getFirstName()).orElse(""));
            map.put(UserCenterConstant.LAST_NAME, Optional.ofNullable(dto.getLastName()).orElse(""));
            map.put(UserCenterConstant.PRODUCT_NAME, Optional.ofNullable(dto.getProductName()).orElse(""));
            File file = new File(path + fileName);
            if (!file.exists()) {
                fileName = getMailFileName(templateNameInvite, "en");
            }
            String fileContent = MailUtils.getTemplateMailText(path, fileName, map);
            sendEmail(fileContent, dto.getEmail());
        } catch (Exception ex) {
            log.error("sendInviteRegister error! email:{}, exception: ", dto.getEmail(),  ex);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SEND_MAIL_ERROR, ex.getMessage());
        }
    }

    public void sendEmail(String fileContent, String sendTo) throws MessagingException, IOException {
        String from = getFrom();
        String subject = "";
        Matcher m = EMAIL_TITLE.matcher(fileContent);
        if (m.find()) {
            subject = m.group(0);
        }
        // 构建邮件内容
        Session session = Session.getDefaultInstance(new Properties());
        MimeMessage mimeMessage = new MimeMessage(session);
        mimeMessage.setSubject(subject, StandardCharsets.UTF_8.name());
        mimeMessage.setFrom(new InternetAddress(from));
        mimeMessage.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(sendTo));
        MimeMultipart multipart = new MimeMultipart("related");

        // 添加邮件内容
        BodyPart messageBodyPart = new MimeBodyPart();
        messageBodyPart.setContent(fileContent, "text/html;charset=utf-8;");
        multipart.addBodyPart(messageBodyPart);

        //添加图片附件
        MimeBodyPart imagePart = new MimeBodyPart();
        String iPath = imgPath + ATTACH_PNG;
        String partFileName = new ClassPathResource("").getPath() + File.separator + ATTACH_PNG;
        FileUtils.writeFile(iPath, partFileName);
        DataSource fds = new FileDataSource(partFileName);
        imagePart.setDataHandler(new DataHandler(fds));
        imagePart.setHeader("Content-ID", "<attach>");
        imagePart.setHeader("Content-Type", "image/png");
        imagePart.setDisposition(Part.INLINE);
        imagePart.setFileName(ATTACH_PNG);
        multipart.addBodyPart(imagePart);
        mimeMessage.setContent(multipart);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        mimeMessage.writeTo(outputStream);

        // 发送邮件
        SendRawEmailRequest rawEmailRequest = new SendRawEmailRequest()
                .withRawMessage(new RawMessage().withData(ByteBuffer.wrap(outputStream.toByteArray())))
                .withSource(from);
        AmazonSimpleEmailService sendEmailClient = getSendEmailClient();
        sendEmailClient.sendRawEmail(rawEmailRequest);
    }

}
