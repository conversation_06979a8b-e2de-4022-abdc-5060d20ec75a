package com.chervon.usercenter.infrastructure.repository;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.SfProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.usercenter.api.dto.sf.SfUserAddDto;
import com.chervon.usercenter.api.dto.sf.au.AuSfUserAddDto;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.vo.sf.*;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.constant.DataCenterRegionEnum;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.infrastructure.config.SfUserConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * SF平台同步操作，北美方向
 * <AUTHOR>
 * @date 2024/6/19
 *
 */
@Service(UserCenterConstant.AU)
@DubboService(group = UserCenterConstant.AU)
@Slf4j
public class SaleForceService4AuImpl extends SaleForceServiceImpl implements SaleForceService {
    private static final String SERVICES_DATA_URI = "/services/data/";
    @Autowired
    private SfUserConfig sfUserConfig;

    @Autowired
    private SfProperties sfProperties;


    @Override
    public String addSfUser(SfUserAddDto sfUserAddDto) {
        AuSfUserAddDto  auSfUserAddDto= ConvertUtil.convert(sfUserAddDto,AuSfUserAddDto.class);
        auSfUserAddDto.setSite_Origin__c(sfProperties.getCountryOfOriginC());
        if (!org.springframework.util.StringUtils.hasText(auSfUserAddDto.getLastName()) || !org.springframework.util.StringUtils.hasText(auSfUserAddDto.getFirstName())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_ADD_USER_ERROR, "LastName or firstName are missing");
        }
        //先发起查询sf有没有该用户
        SfUserRecord sfUserRecord=getSfUserBySfEmail(auSfUserAddDto.getUsername__c());
        if(Objects.nonNull(sfUserRecord)){
            return sfUserRecord.getSfUserId();
        }
        final ResponseEntity<SfUserAddVo> response = getSfUserAddVoResponseEntity(auSfUserAddDto,false);
        if (response == null) {
            return null;
        }
        return response.getBody().getId();
    }

    @Nullable
    private ResponseEntity<SfUserAddVo> getSfUserAddVoResponseEntity(AuSfUserAddDto auSfUserAddDto,Boolean refresh) {
        try {
            SfTokenVo sfTokenVo = getSfToken(refresh);
            RestTemplate restTemplate = getRestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
            String url = sfTokenVo.getInstance_url() + SERVICES_DATA_URI + sfUserConfig.getApiVersion() + "/sobjects/Account";
            log.info("SaleForceService4AuImpl#addSfUser -> create user: {}", JsonUtils.toJson(auSfUserAddDto));
            ResponseEntity<SfUserAddVo> response = restTemplate.postForEntity(url, new HttpEntity<>(auSfUserAddDto, headers), SfUserAddVo.class);
            if (null == response.getBody()) {
                log.error("SaleForceServiceImpl#addSfUser -> failed to create user: return body is empty");
                return null;
            }
            if (!response.getBody().getSuccess()) {
                log.error("SaleForceServiceImpl#addSfUser -> failed to create user: {}", response.getBody().getErrors());
                return null;
            }
            return response;
        } catch (Exception e) {
            if(!refresh && (e.getMessage().contains("INVALID_SESSION_ID") || e.getMessage().contains("Unauthorized"))){
                return getSfUserAddVoResponseEntity(auSfUserAddDto,true);
            }
            return null;
        }
    }

    @Override
    public SfUserRecord getSfUserBySfEmail(String email) {
        List<SfUserRecord> sfUserRecords = listSfUser("+WHERE+EGO_username__c='" + email + "'");
        if (CollectionUtils.isEmpty(sfUserRecords)) {
            return null;
        }
        //判断是不是澳洲的用户,报错提示
        SfUserRecord sfUserRecord = sfUserRecords.get(CommonConstant.ZERO);
        if(!StringUtils.equals(DataCenterRegionEnum.AUSTRALIA.getName(),sfUserRecord.getSiteOriginPc())){
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_USER_REGION_ERROR, email);
        }
        return sfUserRecord;
    }


    @Override
    public List<SfUserRecord> listSfUserUpdatedIn5Min() {
        //根据时间和地区获取用户
        String userSyncTime = getUserSyncTime();
        StringBuilder conditionStringBuilder = new StringBuilder();
        conditionStringBuilder.append("+WHERE+")
                .append("EGO_username__c+!=+NULL+")
                .append("and+EGO_password__c+!=+NULL+")
                .append("and+LastModifiedDate+>+")
                .append(userSyncTime)
                .append(" and Site_Origin__pc='")
                .append(sfProperties.getCountryOfOriginC())
                .append("'");
        return listSfUser(conditionStringBuilder.toString());
    }

    @Override
    public List<SfWarrantyRecord> listSfWarrantyLastUpdated(Long startTime) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = initHeader(sfTokenVo.getAccess_token());
        String timeQueryStr = DateFormatUtils.format(startTime, SF_DATETIME_FORMAT, TimeZone.getTimeZone("GMT+00:00"));
        String url = sfTokenVo.getInstance_url() + SERVICES_DATA_URI + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT Id,Purchase_Date__c,Place_of_Purchase_picklist__c,Place_of_Purchase__c,Product_Use_Type2__c," +
                "AccountCustomer__c,LastModifiedDate,Image_of_Receipt__c,Lost_Receipt__c,Pending__c,Gift__c," +
                "(SELECT Id,Serial_Number__c FROM Warranty_Items__r),Master_Product__c" +
                "+FROM+Warranty__c+WHERE+AccountCustomer__c+!=+NULL" +
                "+AND+Brand_Name__c+=+'EGO'+AND+LastModifiedDate+>=+"+timeQueryStr+"+and+AccountCustomer__r.Site_Origin__pc='"+sfProperties.getCountryOfOriginC()+"'";
        ParameterizedTypeReference<SfQueryVo<Warranty>> responseBodyType =
                new ParameterizedTypeReference<SfQueryVo<Warranty>>() {
                };
        ResponseEntity<SfQueryVo<Warranty>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        SfQueryVo<Warranty> body = response.getBody();
        if(null == body) {
            return Collections.emptyList();
        }

        List<Warranty> records = new ArrayList<>();
        if(null != body.getRecords()) {
            records = body.getRecords();
        }
        while(!body.getDone()) {
            String nextRecordsUrl = sfTokenVo.getInstance_url() + body.getNextRecordsUrl();
            response = restTemplate.exchange(nextRecordsUrl,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    responseBodyType);
            body = response.getBody();
            records.addAll(Objects.requireNonNull(body).getRecords());
        }

        return convert(records);
    }

    private List<SfWarrantyRecord> convert(List<Warranty> records) {
        List<SfWarrantyRecord> list = new ArrayList<>();
        for(Warranty warranty : records) {
            if(warranty.getWarrantyItems() == null || warranty.getWarrantyItems().getTotalSize() <= 0) {
                continue;
            }
            SfWarrantyRecord sfWarrantyRecord = ConvertUtil.convert(warranty, SfWarrantyRecord.class);
            sfWarrantyRecord.setReceiptStatus(warranty.getGift()?"GIFT":"");
            List<WarrantyItems> warrantyItems = warranty.getWarrantyItems().getRecords();
            sfWarrantyRecord.setSn(warrantyItems.get(0).getSn());
            sfWarrantyRecord.setId(warrantyItems.get(0).getId());
            list.add(sfWarrantyRecord);
            if(warrantyItems.size() > 1) {
                for(int i = 1; i < warrantyItems.size(); i++) {
                    SfWarrantyRecord sfWarrantyRecord2 = new SfWarrantyRecord();
                    BeanUtils.copyProperties(sfWarrantyRecord, sfWarrantyRecord2);
                    sfWarrantyRecord2.setSn(warrantyItems.get(i).getSn());
                    sfWarrantyRecord2.setId(warrantyItems.get(i).getId());
                    list.add(sfWarrantyRecord2);
                }
            }
        }
        return list;
    }

    @Override
    public SfQueryVo<SfWarrantyRecord> batchSfWarranty(String queryUrl) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = initHeader(sfTokenVo.getAccess_token());
        if(Strings.isNullOrEmpty(queryUrl)) {
            queryUrl =  SERVICES_DATA_URI + sfUserConfig.getApiVersion() + "/query?q=" +
                    "SELECT+Id,Purchase_Date__c,Place_of_Purchase_picklist__c," +
                    "Place_of_Purchase__c,Product_Use_Type2__c," +
                    "AccountCustomer__c,LastModifiedDate,Image_of_Receipt__c,Lost_Receipt__c,Pending__c,Gift__c," +
                    "(SELECT Id,Serial_Number__c FROM Warranty_Items__r),Master_Product__c" +
                    "+FROM+Warranty__c+WHERE+AccountCustomer__c+!=+NULL+AND+Brand_Name__c+=+'EGO'+and+AccountCustomer__r.Site_Origin__pc='"+sfProperties.getCountryOfOriginC()+"'";
        }
        String url = sfTokenVo.getInstance_url() + queryUrl;
        try {
            ResponseEntity<SfQueryVo<Warranty>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    new ParameterizedTypeReference<SfQueryVo<Warranty>>(){});
            SfQueryVo<Warranty> body = response.getBody();
            SfQueryVo<SfWarrantyRecord> result = new SfQueryVo<>();
            result.setDone(true);
            result.setTotalSize(0);
            result.setRecords(Collections.emptyList());
            if(null == body) {
                return result;
            }

            result.setDone(body.getDone());
            result.setTotalSize(body.getTotalSize());
            result.setNextRecordsUrl(body.getNextRecordsUrl());
            result.setErrorCode(body.getErrorCode());
            if(body.getTotalSize() <= 0) {
                return result;
            }
            result.setRecords(convert(body.getRecords()));
            return result;
        } catch (Exception e) {
            log.error("listSfWarranty, queryUrl:{}, error: ", queryUrl, e);
            return null;
        }
    }

    @Override
    public List<SfWarrantyRecord> getWarrantyBySn(String sn) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        String url = sfTokenVo.getInstance_url() + SERVICES_DATA_URI + sfUserConfig.getApiVersion() + "/query?" +
                "q=SELECT Id,Expiration_Date__c,Purchase_Date__c,Place_of_Purchase__c,Place_of_Purchase_picklist__c," +
                "AccountCustomer__c,Image_of_Receipt__c,Lost_Receipt__c,Pending__c,Gift__c," +
                "LastModifiedDate,(SELECT Id,Serial_Number__c FROM Warranty_Items__r)," +
                "Product_Use_Type__c,Product_Use_Type2__c FROM Warranty__c+" +
                "WHERE+AccountCustomer__c+!=+NULL+AND+Brand_Name__c+=+'EGO'+AND+" +
                "Id IN (SELECT Warranty__c FROM Warranty_Item__c WHERE+Serial_Number__c = '" + sn + "')+and+AccountCustomer__r.Site_Origin__pc='"+sfProperties.getCountryOfOriginC()+"'";
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = initHeader(sfTokenVo.getAccess_token());
        ParameterizedTypeReference<SfQueryVo<Warranty>> responseBodyType =
                new ParameterizedTypeReference<SfQueryVo<Warranty>>() {
                };
        ResponseEntity<SfQueryVo<Warranty>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        SfQueryVo<Warranty> body = response.getBody();
        if(null == body) {
            return Collections.emptyList();
        }
        List<Warranty> records = body.getRecords();
        if(CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        List<SfWarrantyRecord> convert = convert(records);
        if(convert.isEmpty()) {
            return Collections.emptyList();
        }

        //过滤掉同一个用户重复的质保，保留最新
        Map<String, SfWarrantyRecord> map = new HashMap<>(convert.size());
        for(SfWarrantyRecord sfWarrantyRecord : convert) {
            SfWarrantyRecord cacheRecord = map.get(sfWarrantyRecord.getSfUserId());
            if(cacheRecord == null) {
                map.put(sfWarrantyRecord.getSfUserId(), sfWarrantyRecord);
                continue;
            }
            if(sfWarrantyRecord.getLastModifiedDate().compareTo(cacheRecord.getLastModifiedDate()) > 0) {
                map.put(sfWarrantyRecord.getSfUserId(), sfWarrantyRecord);
            }
        }
        return new ArrayList<>(map.values());
    }

    @Override
    public List<SfWarrantyRecord> getWarrantyByUser(String sfUserId) {
        if(StringUtils.isEmpty(sfUserId)) {
            return Collections.emptyList();
        }
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        String url = sfTokenVo.getInstance_url() + SERVICES_DATA_URI + sfUserConfig.getApiVersion() + "/query?" +
                "q=SELECT Id,Expiration_Date__c,Purchase_Date__c,Place_of_Purchase__c,Place_of_Purchase_picklist__c," +
                "AccountCustomer__c,Image_of_Receipt__c,Lost_Receipt__c,Pending__c,Gift__c," +
                "LastModifiedDate,(SELECT Id,Serial_Number__c FROM Warranty_Items__r)," +
                "Product_Use_Type__c,Product_Use_Type2__c FROM Warranty__c WHERE+Brand_Name__c+=+'EGO'+AND+" +
                "AccountCustomer__c='" + sfUserId + "'+and+AccountCustomer__r.Site_Origin__pc='"+sfProperties.getCountryOfOriginC()+"'";
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = initHeader(sfTokenVo.getAccess_token());
        ParameterizedTypeReference<SfQueryVo<Warranty>> responseBodyType =
                new ParameterizedTypeReference<SfQueryVo<Warranty>>() {};
        ResponseEntity<SfQueryVo<Warranty>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        SfQueryVo<Warranty> body = response.getBody();
        if(null == body) {
            return Collections.emptyList();
        }
        List<Warranty> records = body.getRecords();
        if(CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return convert(records);
    }

    @Data
    private static class Warranty {
        @JsonProperty("Id")
        private String id;

        @JsonProperty("AccountCustomer__c")
        private String sfUserId;

        /**
         * 字符串格式传入：yyyy-mm-dd
         */
        @JsonProperty("Purchase_Date__c")
        private String purchaseDate;

        /**
         * 选填：
         * Industrial/Professional/Commercial
         * Residential
         * Rental
         */
        @JsonProperty("Product_Use_Type2__c")
        private String useType;

        /**
         * 购买地址
         */
        @JsonProperty("Place_of_Purchase_picklist__c")
        private String purchasePlace;

        /**
         * 其他购买地址
         */
        @JsonProperty("Place_of_Purchase__c")
        private String purchasePlaceOther;

        /**
         * 发票状态。三选一
         */
        @JsonProperty("Lost_Receipt__c")
        private Boolean lost;

        /**
         * 发票状态。三选一
         */
        @JsonProperty("Pending__c")
        private Boolean pending;

        /**
         * 请查看本文档中的‘WarrantyItems’
         * 部分信息
         */
        @JsonProperty("Gift__c")
        private Boolean gift;

        /**
         * 收据
         */
        @JsonProperty("Image_of_Receipt__c")
        private String receiptUrl;

        /**
         * SF平台质保更新时间
         */
        @JsonProperty("LastModifiedDate")
        private String lastModifiedDate;

        /**
         * 要注册质保的物品(设备)列表
         */
        @JsonProperty("Warranty_Items__r")
        private SfQueryVo<WarrantyItems> warrantyItems;
    }

    @Data
    private static class WarrantyItems {
        @JsonProperty("Serial_Number__c")
        private String sn;
        @JsonProperty("Id")
        private String id;
    }
    @Override
    public SfUserRecord getSfUserBySfUserId(String sfUserId) {
        StringBuilder whereStringBuilder = new StringBuilder()
                .append("+WHERE+")
                .append("EGO_username__c+!=+NULL+")
                .append("and+")
                .append("EGO_password__c+!=+NULL+")
                .append("and+")
                .append("Id+='")
                .append(sfUserId)
                .append("'")
                .append(" and Site_Origin__pc='")
                .append(sfProperties.getCountryOfOriginC())
                .append("'");
        List<SfUserRecord> sfUserRecords = listSfUser(whereStringBuilder.toString());
        if (CollectionUtils.isEmpty(sfUserRecords)) {
            return null;
        }
        return sfUserRecords.get(CommonConstant.ZERO);
    }

    /**
     * 实例化header数据
     * @param accessToken
     * @return
     */
    private HttpHeaders initHeader(String accessToken){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + accessToken);
        return headers;
    }
}
