package com.chervon.usercenter.application.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.AesUtils;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.usercenter.api.dto.UserLoginDto;
import com.chervon.usercenter.api.dto.enums.AppPresenceEnum;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.UserVo;
import com.chervon.usercenter.api.vo.sf.SfUserRecord;
import com.chervon.usercenter.application.assembler.UserVoConverter;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.domain.model.user.Email;
import com.chervon.usercenter.domain.model.user.User;
import com.chervon.usercenter.domain.model.user.UserRepository;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import com.chervon.usercenter.infrastructure.mapper.UserMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-06-14
 */
@DubboService
@Slf4j
@Service
public class UserQueryServiceImpl implements UserQueryService {

    @Resource
    private UserMapper userMapper;

    @Autowired
    private UserRepository userRepository;

    @Resource(name = "${sf.direction}")
    private SaleForceService saleForceService;

    @Autowired
    private AwsProperties awsProperties;


    @Override
    public boolean checkEmailUsed(String email) {
        List<UserDo> list = userMapper.selectList(new LambdaQueryWrapper<UserDo>()
                .eq(UserDo::getEmail, email));
        return !list.isEmpty();
    }

    @Override
    public UserVo login(UserLoginDto dto) {
        User user = userRepository.find(new Email(dto.getEmail()));
        if (user == null) {
            SfUserRecord sfUserRecord = saleForceService.getSfUserBySfEmail(dto.getEmail());
            if (sfUserRecord != null) {
                log.warn("User: {} is not exists in IOT, try to add it from SF.", dto.getEmail());
             }
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_ACCOUNT_PASSWORD_ERROR1, dto.getEmail());
        }
        // 判断账户是否处于激活状态
        if (!user.isActivation()) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_ACCOUNT_DISABLE, dto.getEmail());
        }
        // 从redis中获取临时秘钥
        String key = UserCenterConstant.USER_AES_PASSWORD + dto.getEmail();
        String aesPassword = RedisUtils.getCacheObject(key);
        String sourcePassword = "";
        if (StringUtils.isNotEmpty(aesPassword)) {
            sourcePassword = AesUtils.decrypt(dto.getPassword(), aesPassword);
        }
        if (StringUtils.isEmpty(sourcePassword)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_ACCOUNT_PASSWORD_ERROR2);
        }
        boolean isValid = user.validPassword(sourcePassword, user.getPassword());
        if (!isValid) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_ACCOUNT_PASSWORD_ERROR3);
        }
        // 非EGO用户且邮箱验证字段为否时抛异常，需要去调用发送验证邮箱接口
        if (!Objects.equals(user.getUserSourceCode(), "ego") && Objects.equals(user.getIsEmailValidated(), CommonConstant.ZERO)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_NOT_EGO_USER_FIRSTLY_LOGIN);
        }
        // 异步更新用户的AES密码盐值和在线状态
        userRepository.updateAesPasswordAndSaltWhileLogin(user.getUserId(), dto.getPassword(), aesPassword, AppPresenceEnum.OFFLINE.getDesc());
        return UserVoConverter.fromUser(user, awsProperties.getPictureBucket().getCdnHost());
    }

    @Override
    public UserVo getUserInfo(String email) {
        UserDo userDo = userMapper.selectOne(new LambdaQueryWrapper<UserDo>()
                .eq(UserDo::getEmail, email));
        if (userDo == null) {
            log.error(String.format(UserCenterErrorCode.USER_CENTER_USER_NOT_EXIST.getErrorMessage(), email));
            return null;
        }
        UserVo userVo = ConvertUtil.convert(userDo, UserVo.class);
        userVo.setPhoto(StringUtils.isEmpty(userVo.getPhoto()) ? null : UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), userVo.getPhoto()));
        return userVo;
    }

    @Override
    public List<UserVo> getUserInfoByLike(String email) {
        List<UserDo> userDos = userMapper.selectList(new LambdaQueryWrapper<UserDo>()
                .like(UserDo::getEmail, email));
        if (CollectionUtil.isEmpty(userDos)) {
            log.error(String.format(UserCenterErrorCode.USER_CENTER_USER_NOT_EXIST.getErrorMessage(), email));
            return Lists.newArrayList();
        }
        List<UserVo> userVos = Lists.newArrayList();
        userDos.forEach(userDo -> {
            UserVo userVo = ConvertUtil.convert(userDo, UserVo.class);
            userVos.add(userVo);
        });
        return userVos;
    }

    @Override
    public UserVo getUserInfo(Long userId) {
        UserDo userDo = userMapper.selectById(userId);
        if (userDo == null) {
            log.error(String.format(UserCenterErrorCode.USER_CENTER_USER_NOT_EXIST.getErrorMessage(), userId));
            return null;
        }
        UserVo userVo = ConvertUtil.convert(userDo, UserVo.class);
        userVo.setPhoto(StringUtils.isEmpty(userVo.getPhoto()) ? null : UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), userVo.getPhoto()));
        return userVo;
    }

    @Override
    @Async
    public void renewUserAes() {
        // TODO 压测临时代码,待删除
        List<UserDo> randomUsers = userRepository.list(
                new LambdaQueryWrapper<UserDo>().like(UserDo::getFirstName, "randomUser").select(UserDo::getEmail));
        String value = "k3eKROY1w8Hf1Tv1";
        randomUsers.forEach(randomUser -> {
            String key = UserCenterConstant.USER_AES_PASSWORD + randomUser.getEmail();
            String valueFromRedis = RedisUtils.getCacheObject(key);
            if (!StringUtils.isEmpty(valueFromRedis) && valueFromRedis.equals(value)) {
                RedisUtils.expire(key, Duration.ofDays(30));
            } else {
                RedisUtils.setCacheObject(key, value, Duration.ofDays(30));
            }
        });
    }

    @Override
    public Long getUserIdBySfUserId(String sfUserId) {
        if(Strings.isNullOrEmpty(sfUserId)) {
            return null;
        }
        UserDo userDo = userMapper.selectOne(new LambdaQueryWrapper<UserDo>()
                .select(UserDo::getId)
                .eq(UserDo::getSfUserId, sfUserId));
        return userDo==null?null:userDo.getId();
    }
    @Override
    public Map<Long,UserVo> listUserMap(List<Long> userIds){
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<UserDo> wrapper = new LambdaQueryWrapper<UserDo>()
                .select(UserDo::getId,
                        UserDo::getFirstName,
                        UserDo::getLastName,
                        UserDo::getEmail,
                        UserDo::getPhoto)
                .in(UserDo::getId, userIds);
        List<UserDo> userDos = userRepository.list(wrapper);
        if (CollectionUtils.isEmpty(userDos)) {
            return Collections.emptyMap();
        }
        List<UserVo> appUserVos = new ArrayList<>();
        userDos.forEach(userDo -> {
            UserVo appUserVo = ConvertUtil.convert(userDo, UserVo.class);
            appUserVo.setPhoto(StringUtils.isEmpty(appUserVo.getPhoto()) ?
                    null : UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), appUserVo.getPhoto()));
            appUserVos.add(appUserVo);
        });
        return appUserVos.stream().collect(Collectors.toMap(UserVo::getId, Function.identity()));
    }

    @Override
    public Long getUserIdByEmail(String email) {
        UserDo userDo = userMapper.selectOne(new LambdaQueryWrapper<UserDo>()
                        .select(UserDo::getId)
                .eq(UserDo::getEmail, email));
        if (userDo == null) {
            log.error(String.format(UserCenterErrorCode.USER_CENTER_USER_NOT_EXIST.getErrorMessage(), email));
            return null;
        }
        return userDo.getId();
    }
}
