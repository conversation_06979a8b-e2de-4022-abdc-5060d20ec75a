package com.chervon.iot.app.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.json.JSONUtil;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dto.share.AppDeviceAcceptDto;
import com.chervon.iot.app.domain.dto.share.AppDeviceShareDto;
import com.chervon.iot.app.domain.dto.share.DeviceShareAddDto;
import com.chervon.iot.app.domain.dto.share.DeviceShareSubDto;
import com.chervon.iot.app.domain.enums.ShareTypeEnum;
import com.chervon.usercenter.api.vo.UserVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.fail;
import static org.junit.jupiter.api.Assertions.assertTrue;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest
public class DeviceShareServiceIntegrationTest {

    @Autowired
    DeviceShareService deviceShareService;

    private static final String TEST_FAIL_MSG = "Test failed with exception: ";

    /**
     * 主用户分享设备
     */
    @Test
    void testAddShare() {
        String content = "{" +
                "    \"deviceId\": \"XQF992401000021\"," +
                "    \"email\": \"<EMAIL>\"" +
                "}";
        String language = "fr";
        Long userId = 1856971096187637762L;
        try {
            LocaleContextHolder.setLocale(new Locale(language));
            StpUtil.login(userId);
            String tokenValue = StpUtil.getTokenValue();
            UserVo appUserVo = new UserVo();
            appUserVo.setId(userId);
            appUserVo.setEmail("<EMAIL>");
            appUserVo.setFirstName("first");
            appUserVo.setLastName("last");
            RedisUtils.setCacheObject(tokenValue, appUserVo);
            DeviceShareAddDto deviceShareAddDto = JSONUtil.toBean(content, DeviceShareAddDto.class);
            deviceShareService.share(deviceShareAddDto);
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }

    }

    @Test
    void testShareAndInvite() {
        Long userId = 1856971096187637762L;
        String content = "{" +
                "    \"deviceId\": \"XQF992401000021\"," +
                "    \"email\": \"<EMAIL>\"" +
                "}";
        String language = "en";
        try {
            StpUtil.login(userId);
            LocaleContextHolder.setLocale(new Locale(language));
            DeviceShareAddDto deviceShareAddDto = JSONUtil.toBean(content, DeviceShareAddDto.class);
            deviceShareService.shareAndInvite(deviceShareAddDto);
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }

    }

    /**
     * 子用户确认接受设备分享
     */
    @Test
    void testAcceptShare() {
        String userId = "1860936459630604290";
        Long shareId = 1860937105758158850L;
        try {
            StpUtil.login(userId);
            deviceShareService.accept(shareId);
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }

    @Test
    void testAcceptDeviceList() {
        // 测试用的用户id
        String userId = "1860936459630604290";
        try {
            // 登录
            StpUtil.login(userId);
            // 分享设备列表
            List<AppDeviceAcceptDto> appDeviceAcceptDtos = deviceShareService.acceptDeviceList();
            // 断言：验证分享的设备列表不为空
            assertNotNull(appDeviceAcceptDtos, "Shared accept list should not be null");
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }

    @Test
    void testSubList() {
        // 测试用的用户id
        String userId = "1856971096187637762";
        String deviceId = "XRM586330874963";
        try {
            // 登录
            StpUtil.login(userId);
            // 分享设备列表
            final List<DeviceShareSubDto> deviceShareSubDtos = deviceShareService.subList(deviceId);
            // 断言：验证分享的设备列表不为空
            assertNotNull(deviceShareSubDtos, "Shared accept list should not be null");
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }

    /**
     * 主用户删除设备分享
     */
    @Test
    void testMasterRemoveShare() {
        String userId = "1856971096187637762";
        Long shareId = 1860937105758158850L;
        try {
            // 登录
            StpUtil.login(userId);
            deviceShareService.masterRemoveShare(shareId);
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }

    /**
     * 子用户删除设备分享
     */
    @Test
    void testSubRemoveShare() {
        try {
            deviceShareService.subRemoveShare(1828264505534689282L);
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }

    /**
     * 用户设备解绑后清空分享信息
     */
    @Test
    void testClearShare() {
        AppUserDevice appUserDevice = new AppUserDevice();
        appUserDevice.setDeviceId("XHT112288252603");
        appUserDevice.setUserId(1805073516484632577L);
        appUserDevice.setShareType(ShareTypeEnum.MASTER.getValue());
        try {
            deviceShareService.clearShare(appUserDevice);
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }

    /**
     * 待接受分享数统计
     */
    @Test
    void pendingCountTest() {
        try {
            final long l = deviceShareService.pendingCount();
            assertTrue(l > 0, "pending count not be less than 0");
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }

    /**
     * 处理设备分享过期记录
     */
    @Test
    void expireTest() {
        try {
            deviceShareService.expire();
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }

    /**
     * 用户注册完成后，推送分享通知
     */
    @Test
    void updateAfterRegisterTest() {
        String email = "<EMAIL>";
        Long userId = 1860936459630604290L;
        try {
            deviceShareService.updateAfterRegister(email, userId);
        } catch (Exception e) {
            fail(TEST_FAIL_MSG + e.getMessage());
        }

    }

    @Test
    void shareDeviceList() {
        // 获取测试用的手机号码
        String userId = "1856971096187637762";
        try {
            // 登录
            StpUtil.login(userId);
            // 分享设备列表
            List<AppDeviceShareDto> sharedDevices = deviceShareService.shareDeviceList();
            // 断言：验证分享的设备列表不为空
            assertNotNull(sharedDevices, "Shared device list should not be null");
        } catch (Exception e) {
            // 记录异常信息
            fail(TEST_FAIL_MSG + e.getMessage());
        }
    }
}
