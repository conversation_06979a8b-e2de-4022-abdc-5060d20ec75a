package com.chervon.iot.app.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.iot.app.domain.dto.explore.ExploreBannerDto;
import com.chervon.iot.app.domain.dto.explore.HaveFunDto;
import com.chervon.iot.app.domain.dto.explore.NewProductDto;
import com.chervon.iot.app.domain.dto.explore.PromotionProductDto;
import com.chervon.iot.app.domain.vo.explore.HaveFunReq;
import com.chervon.iot.app.service.ExploreService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ExploreController单元测试类
 *
 * 测试覆盖：
 * 1. 获取Explore首页数据的各种场景
 * 2. 获取HaveFun内容列表的各种场景
 * 3. 异常情况处理
 * 4. 边界条件测试
 *
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ExploreController单元测试")
public class ExploreControllerTest {

    @InjectMocks
    private ExploreController exploreController;

    @Mock
    private ExploreService exploreService;

    private static final Long USER_ID = 123L;
    private static final String IMAGE_URL = "https://example.com/image.jpg";
    private static final String SHOP_URL = "https://example.com/shop";
    private static final String CONTENT_NO_1 = "HF001";
    private static final String CONTENT_NO_2 = "HF002";

    @Nested
    @DisplayName("获取Explore首页数据测试")
    class GetExploreHomeDataTest {

        @Test
        @DisplayName("正常获取首页数据 - 应该返回完整的Banner数据")
        void testGetExploreHomeData_Success() {
            // 准备测试数据
            ExploreBannerDto mockBannerDto = createMockExploreBannerDto();

            // 使用MockedStatic模拟StpUtil的静态方法
            try (MockedStatic<StpUtil> stpUtilMocked = Mockito.mockStatic(StpUtil.class)) {
                // 模拟StpUtil.getLoginIdAsLong()返回用户ID
                stpUtilMocked.when(StpUtil::getLoginIdAsLong).thenReturn(USER_ID);

                // 模拟ExploreService.getExploreHomeData方法
                when(exploreService.getExploreHomeData(USER_ID)).thenReturn(mockBannerDto);

                // 调用控制器方法
                R<ExploreBannerDto> response = exploreController.getExploreHomeData();

                // 验证StpUtil.getLoginIdAsLong()被调用
                stpUtilMocked.verify(StpUtil::getLoginIdAsLong, times(1));

                // 验证ExploreService.getExploreHomeData方法被正确调用
                verify(exploreService, times(1)).getExploreHomeData(USER_ID);

                // 验证响应结果
                assertNotNull(response, "响应不应为null");
                assertTrue(response.isStatus(), "响应应该成功");
                assertNotNull(response.getEntry(), "响应数据不应为null");

                // 验证响应数据内容
                ExploreBannerDto resultDto = response.getEntry();
                assertNotNull(resultDto.getNewProduct(), "新品信息不应为null");
                assertEquals(IMAGE_URL, resultDto.getNewProduct().getImageUrl(), "新品图片URL应匹配");
                assertEquals(SHOP_URL, resultDto.getNewProduct().getShopUrl(), "新品购买链接应匹配");

                assertNotNull(resultDto.getPromotionProduct1(), "促销产品1不应为null");
                assertEquals("促销产品1", resultDto.getPromotionProduct1().getProductName(), "促销产品1名称应匹配");

                assertNotNull(resultDto.getPromotionProduct2(), "促销产品2不应为null");
                assertEquals("促销产品2", resultDto.getPromotionProduct2().getProductName(), "促销产品2名称应匹配");
            }
        }

        @Test
        @DisplayName("服务返回null - 应该正常处理")
        void testGetExploreHomeData_ServiceReturnsNull() {
            try (MockedStatic<StpUtil> stpUtilMocked = Mockito.mockStatic(StpUtil.class)) {
                // 模拟StpUtil.getLoginIdAsLong()返回用户ID
                stpUtilMocked.when(StpUtil::getLoginIdAsLong).thenReturn(USER_ID);

                // 模拟ExploreService.getExploreHomeData方法返回null
                when(exploreService.getExploreHomeData(USER_ID)).thenReturn(null);

                // 调用控制器方法
                R<ExploreBannerDto> response = exploreController.getExploreHomeData();

                // 验证响应结果
                assertNotNull(response, "响应不应为null");
                assertTrue(response.isStatus(), "响应应该成功");
                assertNull(response.getEntry(), "响应数据应为null");

                // 验证服务方法被调用
                verify(exploreService, times(1)).getExploreHomeData(USER_ID);
            }
        }

        @Test
        @DisplayName("用户ID为0 - 应该正常处理")
        void testGetExploreHomeData_UserIdIsZero() {
            try (MockedStatic<StpUtil> stpUtilMocked = Mockito.mockStatic(StpUtil.class)) {
                // 模拟StpUtil.getLoginIdAsLong()返回0（表示未登录或无效用户）
                stpUtilMocked.when(StpUtil::getLoginIdAsLong).thenReturn(0L);

                // 模拟ExploreService.getExploreHomeData方法
                when(exploreService.getExploreHomeData(0L)).thenReturn(null);

                // 调用控制器方法
                R<ExploreBannerDto> response = exploreController.getExploreHomeData();

                // 验证响应结果
                assertNotNull(response, "响应不应为null");
                assertTrue(response.isStatus(), "响应应该成功");

                // 验证服务方法被调用
                verify(exploreService, times(1)).getExploreHomeData(0L);
            }
        }
    }

    @Nested
    @DisplayName("获取HaveFun内容列表测试")
    class GetHaveFunContentsTest {

        @Test
        @DisplayName("正常获取HaveFun内容列表 - 应该返回分页数据")
        void testGetHaveFunContents_Success() {
            // 准备测试数据
            HaveFunReq req = createMockHaveFunReq();
            PageResult<HaveFunDto> mockPageResult = createMockHaveFunPageResult();

            // 模拟ExploreService.getHaveFunContents方法
            when(exploreService.getHaveFunContents(req)).thenReturn(mockPageResult);

            // 调用控制器方法
            R<PageResult<HaveFunDto>> response = exploreController.getNewProducts(req);

            // 验证ExploreService.getHaveFunContents方法被正确调用
            verify(exploreService, times(1)).getHaveFunContents(req);

            // 验证响应结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertNotNull(response.getEntry(), "响应数据不应为null");

            // 验证分页数据
            PageResult<HaveFunDto> pageResult = response.getEntry();
            assertEquals(2, pageResult.getTotal(), "总数量应为2");
            assertEquals(1, pageResult.getPageNum(), "当前页码应为1");
            assertEquals(10, pageResult.getPageSize(), "页大小应为10");
            assertEquals(2, pageResult.getList().size(), "列表大小应为2");

            // 验证列表内容
            List<HaveFunDto> haveFunList = pageResult.getList();
            assertEquals(CONTENT_NO_1, haveFunList.get(0).getContentNo(), "第一个内容编号应匹配");
            assertEquals(CONTENT_NO_2, haveFunList.get(1).getContentNo(), "第二个内容编号应匹配");
        }

        @Test
        @DisplayName("服务返回null - 应该正常处理")
        void testGetHaveFunContents_ServiceReturnsNull() {
            // 准备测试数据
            HaveFunReq req = createMockHaveFunReq();

            // 模拟ExploreService.getHaveFunContents方法返回null
            when(exploreService.getHaveFunContents(req)).thenReturn(null);

            // 调用控制器方法
            R<PageResult<HaveFunDto>> response = exploreController.getNewProducts(req);

            // 验证ExploreService.getHaveFunContents方法被正确调用
            verify(exploreService, times(1)).getHaveFunContents(req);

            // 验证响应结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertNull(response.getEntry(), "响应数据应为null");
        }

        @Test
        @DisplayName("空请求参数 - 应该正常处理")
        void testGetHaveFunContents_EmptyRequest() {
            // 准备空请求
            HaveFunReq req = new HaveFunReq();
            PageResult<HaveFunDto> emptyPageResult = createEmptyPageResult();

            // 模拟ExploreService.getHaveFunContents方法
            when(exploreService.getHaveFunContents(req)).thenReturn(emptyPageResult);

            // 调用控制器方法
            R<PageResult<HaveFunDto>> response = exploreController.getNewProducts(req);

            // 验证响应结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertNotNull(response.getEntry(), "响应数据不应为null");

            PageResult<HaveFunDto> pageResult = response.getEntry();
            assertEquals(0, pageResult.getTotal(), "空结果总数量应为0");
            assertTrue(pageResult.getList().isEmpty(), "空结果列表应为空");
        }

        @Test
        @DisplayName("分页参数测试 - 应该正确传递分页信息")
        void testGetHaveFunContents_WithPagination() {
            // 准备测试数据
            HaveFunReq req = new HaveFunReq();
            req.setPageNum(2);
            req.setPageSize(5);

            PageResult<HaveFunDto> mockPageResult = new PageResult<>(2, 5, 10);
            mockPageResult.setList(createMockHaveFunList().subList(0, 1)); // 只返回一个元素模拟第二页

            // 模拟ExploreService.getHaveFunContents方法
            when(exploreService.getHaveFunContents(req)).thenReturn(mockPageResult);

            // 调用控制器方法
            R<PageResult<HaveFunDto>> response = exploreController.getNewProducts(req);

            // 验证响应结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");

            PageResult<HaveFunDto> pageResult = response.getEntry();
            assertEquals(2, pageResult.getPageNum(), "当前页码应为2");
            assertEquals(5, pageResult.getPageSize(), "页大小应为5");
            assertEquals(10, pageResult.getTotal(), "总数量应为10");
            assertEquals(1, pageResult.getList().size(), "第二页应只有一个元素");
        }
    }

    /**
     * 创建模拟的ExploreBannerDto对象
     */
    private ExploreBannerDto createMockExploreBannerDto() {
        ExploreBannerDto bannerDto = new ExploreBannerDto();

        // 设置新品信息
        NewProductDto newProductDto = new NewProductDto();
        newProductDto.setImageUrl(IMAGE_URL);
        newProductDto.setShopUrl(SHOP_URL);
        bannerDto.setNewProduct(newProductDto);

        // 设置促销产品1
        PromotionProductDto promotionProductDto1 = new PromotionProductDto();
        promotionProductDto1.setProductName("促销产品1");
        promotionProductDto1.setProductCategory("割草机");
        promotionProductDto1.setCommodityModel("型号1");
        promotionProductDto1.setImageUrl("https://example.com/images/product1.jpg");
        promotionProductDto1.setShopUrl("https://example.com/shop/product1");
        bannerDto.setPromotionProduct1(promotionProductDto1);

        // 设置促销产品2
        PromotionProductDto promotionProductDto2 = new PromotionProductDto();
        promotionProductDto2.setProductName("促销产品2");
        promotionProductDto2.setProductCategory("吹风机");
        promotionProductDto2.setCommodityModel("型号2");
        promotionProductDto2.setImageUrl("https://example.com/images/product2.jpg");
        promotionProductDto2.setShopUrl("https://example.com/shop/product2");
        bannerDto.setPromotionProduct2(promotionProductDto2);

        return bannerDto;
    }

    /**
     * 创建模拟的HaveFunReq对象
     */
    private HaveFunReq createMockHaveFunReq() {
        HaveFunReq req = new HaveFunReq();
        req.setPageNum(1);
        req.setPageSize(10);
        return req;
    }

    /**
     * 创建模拟的HaveFun分页结果
     */
    private PageResult<HaveFunDto> createMockHaveFunPageResult() {
        List<HaveFunDto> haveFunList = createMockHaveFunList();
        PageResult<HaveFunDto> pageResult = new PageResult<>(1, 10, 2);
        pageResult.setList(haveFunList);
        return pageResult;
    }

    /**
     * 创建空的分页结果
     */
    private PageResult<HaveFunDto> createEmptyPageResult() {
        PageResult<HaveFunDto> pageResult = new PageResult<>(1, 10, 0);
        pageResult.setList(new ArrayList<>());
        return pageResult;
    }

    /**
     * 创建模拟的HaveFunDto列表
     */
    private List<HaveFunDto> createMockHaveFunList() {
        List<HaveFunDto> haveFunList = new ArrayList<>();

        // 创建HaveFunDto对象1
        HaveFunDto haveFunDto1 = new HaveFunDto();
        haveFunDto1.setId(1L);
        haveFunDto1.setContentNo(CONTENT_NO_1);
        haveFunDto1.setTitle("有趣活动1");
        haveFunDto1.setText("这是一个有趣的活动1");
        haveFunDto1.setType("Image");
        haveFunDto1.setAvatarUrl("https://example.com/avatars/avatar1.jpg");
        haveFunDto1.setIvUrl("https://example.com/images/activity1.jpg");
        haveFunDto1.setLinkInText("https://example.com/link1");
        haveFunDto1.setCreateTime(LocalDateTime.now().minusDays(1));
        haveFunDto1.setUpdateTime(LocalDateTime.now());
        haveFunList.add(haveFunDto1);

        // 创建HaveFunDto对象2
        HaveFunDto haveFunDto2 = new HaveFunDto();
        haveFunDto2.setId(2L);
        haveFunDto2.setContentNo(CONTENT_NO_2);
        haveFunDto2.setTitle("有趣活动2");
        haveFunDto2.setText("这是一个有趣的活动2");
        haveFunDto2.setType("Video");
        haveFunDto2.setAvatarUrl("https://example.com/avatars/avatar2.jpg");
        haveFunDto2.setIvUrl("https://example.com/videos/activity2.mp4");
        haveFunDto2.setLinkInText("https://example.com/link2");
        haveFunDto2.setCreateTime(LocalDateTime.now().minusDays(2));
        haveFunDto2.setUpdateTime(LocalDateTime.now().minusHours(1));
        haveFunList.add(haveFunDto2);

        return haveFunList;
    }
}
