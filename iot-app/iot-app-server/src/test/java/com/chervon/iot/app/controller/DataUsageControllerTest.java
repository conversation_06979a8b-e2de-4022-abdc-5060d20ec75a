package com.chervon.iot.app.controller;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.iot.app.domain.dto.device.DeviceUsageReqDto;
import com.chervon.iot.app.domain.dto.device.UsageHistoryReqDto;
import com.chervon.iot.app.domain.vo.device.UsageHistoryVo;
import com.chervon.iot.app.service.DeviceUsageDataService;
import io.lettuce.core.KeyValue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * DataUsageController单元测试类
 *
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DataUsageController单元测试")
class DataUsageControllerTest {

    @Mock
    private DeviceUsageDataService deviceUsageDataService;

    @InjectMocks
    private DataUsageController dataUsageController;

    private DeviceUsageReqDto deviceUsageReqDto;
    private UsageHistoryReqDto usageHistoryReqDto;
    private UsageHistoryVo usageHistoryVo;
    private Object tracksHistoryResult;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        deviceUsageReqDto = new DeviceUsageReqDto();
        deviceUsageReqDto.setDeviceId("TEST_DEVICE_001");
        deviceUsageReqDto.setDate("2024/01/10");

        usageHistoryReqDto = new UsageHistoryReqDto();
        usageHistoryReqDto.setDeviceId("TEST_DEVICE_001");
        usageHistoryReqDto.setDateType(1);
        usageHistoryReqDto.setDatePeriod(7);
        usageHistoryReqDto.setDateValue("2024/01/05");
        usageHistoryReqDto.setBusType(Arrays.asList("workingTime", "CO2Reduction"));

        // 构造返回数据
        usageHistoryVo = new UsageHistoryVo();
        usageHistoryVo.setDeviceId("TEST_DEVICE_001");
        usageHistoryVo.setDateType(1);
        usageHistoryVo.setDateValue("2024/01/05");

        Map<String, Long> totalValue = new HashMap<>(2);
        totalValue.put("workingTime", 1440L);
        totalValue.put("CO2Reduction", 500L);
        usageHistoryVo.setTotalValue(totalValue);

        Map<String, List<KeyValue<String, Long>>> dataList = new HashMap<>(2);
        List<KeyValue<String, Long>> workingTimeData = Arrays.asList(
            KeyValue.just("2024-01-01", 240L),
            KeyValue.just("2024-01-02", 200L)
        );
        dataList.put("workingTime", workingTimeData);
        usageHistoryVo.setDataList(dataList);
        usageHistoryVo.setLastSyncedTime(System.currentTimeMillis());

        tracksHistoryResult = new HashMap<String, Object>() {{
            put("deviceId", "TEST_DEVICE_001");
            put("tracks", Arrays.asList("track1", "track2"));
        }};
    }

    @Test
    @DisplayName("测试historyTracks方法 - 正常情况")
    void testHistoryTracks_Success() {
        // Given
        when(deviceUsageDataService.getTracksHistory(any(DeviceUsageReqDto.class)))
            .thenReturn(tracksHistoryResult);

        // When
        Object result = dataUsageController.historyTracks(deviceUsageReqDto);

        // Then
        assertNotNull(result);
        assertEquals(tracksHistoryResult, result);
        verify(deviceUsageDataService, times(1)).getTracksHistory(deviceUsageReqDto);
    }

    @Test
    @DisplayName("测试historyTracks方法 - deviceId为空")
    void testHistoryTracks_DeviceIdEmpty() {
        // Given
        deviceUsageReqDto.setDeviceId("");

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.historyTracks(deviceUsageReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getTracksHistory(any(DeviceUsageReqDto.class));
    }

    @Test
    @DisplayName("测试historyTracks方法 - deviceId为null")
    void testHistoryTracks_DeviceIdNull() {
        // Given
        deviceUsageReqDto.setDeviceId(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.historyTracks(deviceUsageReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getTracksHistory(any(DeviceUsageReqDto.class));
    }

    @Test
    @DisplayName("测试historyTracks方法 - date为空")
    void testHistoryTracks_DateEmpty() {
        // Given
        deviceUsageReqDto.setDate("");

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.historyTracks(deviceUsageReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getTracksHistory(any(DeviceUsageReqDto.class));
    }

    @Test
    @DisplayName("测试historyTracks方法 - date为null")
    void testHistoryTracks_DateNull() {
        // Given
        deviceUsageReqDto.setDate(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.historyTracks(deviceUsageReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getTracksHistory(any(DeviceUsageReqDto.class));
    }

    @Test
    @DisplayName("测试usageHistory方法 - 正常情况")
    void testUsageHistory_Success() {
        // Given
        when(deviceUsageDataService.getUsageHistory(any(UsageHistoryReqDto.class)))
            .thenReturn(usageHistoryVo);

        // When
        UsageHistoryVo result = dataUsageController.usageHistory(usageHistoryReqDto);

        // Then
        assertNotNull(result);
        assertEquals(usageHistoryVo, result);
        assertEquals("TEST_DEVICE_001", result.getDeviceId());
        assertEquals(Integer.valueOf(1), result.getDateType());
        assertEquals("2024/01/05", result.getDateValue());
        assertNotNull(result.getTotalValue());
        assertEquals(Long.valueOf(1440L), result.getTotalValue().get("workingTime"));
        assertEquals(Long.valueOf(500L), result.getTotalValue().get("CO2Reduction"));
        assertNotNull(result.getDataList());
        assertNotNull(result.getLastSyncedTime());
        verify(deviceUsageDataService, times(1)).getUsageHistory(usageHistoryReqDto);
    }

    @Test
    @DisplayName("测试usageHistory方法 - deviceId为空")
    void testUsageHistory_DeviceIdEmpty() {
        // Given
        usageHistoryReqDto.setDeviceId("");

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.usageHistory(usageHistoryReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getUsageHistory(any(UsageHistoryReqDto.class));
    }

    @Test
    @DisplayName("测试usageHistory方法 - deviceId为null")
    void testUsageHistory_DeviceIdNull() {
        // Given
        usageHistoryReqDto.setDeviceId(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.usageHistory(usageHistoryReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getUsageHistory(any(UsageHistoryReqDto.class));
    }

    @Test
    @DisplayName("测试usageHistory方法 - dateValue为空")
    void testUsageHistory_DateValueEmpty() {
        // Given
        usageHistoryReqDto.setDateValue("");

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.usageHistory(usageHistoryReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getUsageHistory(any(UsageHistoryReqDto.class));
    }

    @Test
    @DisplayName("测试usageHistory方法 - dateValue为null")
    void testUsageHistory_DateValueNull() {
        // Given
        usageHistoryReqDto.setDateValue(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.usageHistory(usageHistoryReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getUsageHistory(any(UsageHistoryReqDto.class));
    }

    @Test
    @DisplayName("测试usageHistory方法 - dateType为null")
    void testUsageHistory_DateTypeNull() {
        // Given
        usageHistoryReqDto.setDateType(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.usageHistory(usageHistoryReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getUsageHistory(any(UsageHistoryReqDto.class));
    }

    @Test
    @DisplayName("测试usageHistory方法 - datePeriod为null")
    void testUsageHistory_DatePeriodNull() {
        // Given
        usageHistoryReqDto.setDatePeriod(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.usageHistory(usageHistoryReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getUsageHistory(any(UsageHistoryReqDto.class));
    }

    @Test
    @DisplayName("测试usageHistory方法 - busType为null")
    void testUsageHistory_BusTypeNull() {
        // Given
        usageHistoryReqDto.setBusType(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataUsageController.usageHistory(usageHistoryReqDto);
        });

        assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        verify(deviceUsageDataService, never()).getUsageHistory(any(UsageHistoryReqDto.class));
    }

    @Test
    @DisplayName("测试usageHistory方法 - 边界值测试：dateType为1")
    void testUsageHistory_DateTypeOne() {
        // Given
        usageHistoryReqDto.setDateType(1);
        when(deviceUsageDataService.getUsageHistory(any(UsageHistoryReqDto.class)))
            .thenReturn(usageHistoryVo);

        // When
        UsageHistoryVo result = dataUsageController.usageHistory(usageHistoryReqDto);

        // Then
        assertNotNull(result);
        verify(deviceUsageDataService, times(1)).getUsageHistory(usageHistoryReqDto);
    }

    @Test
    @DisplayName("测试usageHistory方法 - 边界值测试：dateType为2")
    void testUsageHistory_DateTypeTwo() {
        // Given
        usageHistoryReqDto.setDateType(2);
        when(deviceUsageDataService.getUsageHistory(any(UsageHistoryReqDto.class)))
            .thenReturn(usageHistoryVo);

        // When
        UsageHistoryVo result = dataUsageController.usageHistory(usageHistoryReqDto);

        // Then
        assertNotNull(result);
        verify(deviceUsageDataService, times(1)).getUsageHistory(usageHistoryReqDto);
    }

    @Test
    @DisplayName("测试usageHistory方法 - 边界值测试：dateType为3")
    void testUsageHistory_DateTypeThree() {
        // Given
        usageHistoryReqDto.setDateType(3);
        when(deviceUsageDataService.getUsageHistory(any(UsageHistoryReqDto.class)))
            .thenReturn(usageHistoryVo);

        // When
        UsageHistoryVo result = dataUsageController.usageHistory(usageHistoryReqDto);

        // Then
        assertNotNull(result);
        verify(deviceUsageDataService, times(1)).getUsageHistory(usageHistoryReqDto);
    }

    @Test
    @DisplayName("测试usageHistory方法 - 边界值测试：datePeriod为最小值1")
    void testUsageHistory_DatePeriodMinValue() {
        // Given
        usageHistoryReqDto.setDatePeriod(1);
        when(deviceUsageDataService.getUsageHistory(any(UsageHistoryReqDto.class)))
            .thenReturn(usageHistoryVo);

        // When
        UsageHistoryVo result = dataUsageController.usageHistory(usageHistoryReqDto);

        // Then
        assertNotNull(result);
        verify(deviceUsageDataService, times(1)).getUsageHistory(usageHistoryReqDto);
    }

    @Test
    @DisplayName("测试usageHistory方法 - 边界值测试：datePeriod为较大值30")
    void testUsageHistory_DatePeriodMaxValue() {
        // Given
        usageHistoryReqDto.setDatePeriod(30);
        when(deviceUsageDataService.getUsageHistory(any(UsageHistoryReqDto.class)))
            .thenReturn(usageHistoryVo);

        // When
        UsageHistoryVo result = dataUsageController.usageHistory(usageHistoryReqDto);

        // Then
        assertNotNull(result);
        verify(deviceUsageDataService, times(1)).getUsageHistory(usageHistoryReqDto);
    }

    @Test
    @DisplayName("测试usageHistory方法 - 边界值测试：busType为空列表")
    void testUsageHistory_BusTypeEmptyList() {
        // Given
        usageHistoryReqDto.setBusType(Arrays.asList());
        when(deviceUsageDataService.getUsageHistory(any(UsageHistoryReqDto.class)))
            .thenReturn(usageHistoryVo);

        // When
        UsageHistoryVo result = dataUsageController.usageHistory(usageHistoryReqDto);

        // Then
        assertNotNull(result);
        verify(deviceUsageDataService, times(1)).getUsageHistory(usageHistoryReqDto);
    }

    @Test
    @DisplayName("测试usageHistory方法 - 边界值测试：busType包含多个类型")
    void testUsageHistory_BusTypeMultipleValues() {
        // Given
        usageHistoryReqDto.setBusType(Arrays.asList(
            "workingTime", "CO2Reduction", "mowingArea", "powerConsumption"
        ));
        when(deviceUsageDataService.getUsageHistory(any(UsageHistoryReqDto.class)))
            .thenReturn(usageHistoryVo);

        // When
        UsageHistoryVo result = dataUsageController.usageHistory(usageHistoryReqDto);

        // Then
        assertNotNull(result);
        verify(deviceUsageDataService, times(1)).getUsageHistory(usageHistoryReqDto);
    }

    @Test
    @DisplayName("测试构造函数")
    void testConstructor() {
        // Given
        DeviceUsageDataService mockService = org.mockito.Mockito.mock(DeviceUsageDataService.class);

        // When
        DataUsageController controller = new DataUsageController(mockService);

        // Then
        assertNotNull(controller);
    }
}