package com.chervon.iot.app.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.iot.app.config.AppProperties;
import com.chervon.iot.app.domain.dto.device.DeviceAlarmDto;
import com.chervon.iot.app.domain.dto.message.MessageSettingDto;
import com.chervon.iot.app.domain.vo.AppLangVo;
import com.chervon.iot.app.domain.vo.message.MessageSettingVo;
import com.chervon.iot.app.service.DeviceAlarmService;
import com.chervon.iot.app.service.UserSettingService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.i18n.LocaleContextHolder;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UserSettingController单元测试类
 *
 * 测试覆盖：
 * 1. 智能开关设置相关接口
 * 2. 消息开关设置相关接口
 * 3. 语言设置相关接口
 * 4. 推送Token设置接口
 * 5. 紧急联系人相关接口
 * 6. 异常情况处理
 * 7. 边界条件测试
 *
 * <AUTHOR> 2024/7/23
 * <AUTHOR> Assistant (optimized)
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("UserSettingController单元测试")
public class UserSettingControllerTest {

    @InjectMocks
    private UserSettingController userSettingController;

    @Mock
    private UserSettingService userSettingService;

    @Mock
    private DeviceAlarmService deviceAlarmService;

    @Mock
    private AppProperties appProperties;

    private static final Long USER_ID = 123L;
    private static final String DEVICE_ID = "TEST_DEVICE_001";
    private static final String PUSH_TOKEN = "test_push_token_123";
    private static final String LANGUAGE_ZH = "zh";
    private static final String LANGUAGE_EN = "en";

    @Nested
    @DisplayName("智能开关设置测试")
    class SmartSwitchTest {

        @Test
        @DisplayName("设置智能开关为true - 应该成功")
        void testSetSmartSwitch_True() {
            // 准备测试数据
            SingleInfoReq<Boolean> req = new SingleInfoReq<>();
            req.setReq(true);

            // 模拟UserSettingService.setSmartSwitch方法
            when(userSettingService.setSmartSwitch(true)).thenReturn(true);

            // 调用控制器方法
            R<Boolean> response = userSettingController.setSmartSwitch(req);

            // 验证结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertTrue(response.getEntry(), "返回结果应该为true");

            // 验证服务方法被调用
            verify(userSettingService, times(1)).setSmartSwitch(true);
        }

        @Test
        @DisplayName("设置智能开关为false - 应该成功")
        void testSetSmartSwitch_False() {
            // 准备测试数据
            SingleInfoReq<Boolean> req = new SingleInfoReq<>();
            req.setReq(false);

            // 模拟UserSettingService.setSmartSwitch方法
            when(userSettingService.setSmartSwitch(false)).thenReturn(false);

            // 调用控制器方法
            R<Boolean> response = userSettingController.setSmartSwitch(req);

            // 验证结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertFalse(response.getEntry(), "返回结果应该为false");

            // 验证服务方法被调用
            verify(userSettingService, times(1)).setSmartSwitch(false);
        }

        @Test
        @DisplayName("获取智能开关状态 - 应该成功")
        void testGetSmartSwitch_Success() {
            // 准备测试数据
            SingleInfoResp<Boolean> mockResp = new SingleInfoResp<>();
            mockResp.setInfo(true);

            // 模拟UserSettingService.getSmartSwitch方法
            when(userSettingService.getSmartSwitch()).thenReturn(mockResp);

            // 调用控制器方法
            R<SingleInfoResp<Boolean>> response = userSettingController.getSmartSwitch();

            // 验证结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertNotNull(response.getEntry(), "响应数据不应为null");
            assertTrue(response.getEntry().getInfo(), "智能开关应该为开启状态");

            // 验证服务方法被调用
            verify(userSettingService, times(1)).getSmartSwitch();
        }
    }

    @Nested
    @DisplayName("消息开关设置测试")
    class MessageSwitchTest {

        @Test
        @DisplayName("设置消息开关 - 应该成功")
        void testSetMessageSwitch_Success() {
            // 准备测试数据
            MessageSettingDto messageSettingDto = createMockMessageSettingDto();

            // 模拟UserSettingService.setMessageSwitch方法
            when(userSettingService.setMessageSwitch(messageSettingDto)).thenReturn(true);

            // 调用控制器方法
            R<Boolean> response = userSettingController.setMessageSwitch(messageSettingDto);

            // 验证结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertTrue(response.getEntry(), "返回结果应该为true");

            // 验证服务方法被调用
            verify(userSettingService, times(1)).setMessageSwitch(messageSettingDto);
        }

        @Test
        @DisplayName("获取消息开关状态 - 应该成功")
        void testGetMessageSwitch_Success() {
            // 准备测试数据
            MessageSettingVo mockVo = createMockMessageSettingVo();

            // 模拟UserSettingService.getMessageSwitch方法
            when(userSettingService.getMessageSwitch()).thenReturn(mockVo);

            // 调用控制器方法
            R<MessageSettingVo> response = userSettingController.getMessageSwitch();

            // 验证结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertNotNull(response.getEntry(), "响应数据不应为null");
            assertEquals(Integer.valueOf(1), response.getEntry().getSystemMessageSwitch(), "系统消息开关应该为1");
            assertEquals(Integer.valueOf(1), response.getEntry().getDeviceMessageSwitch(), "设备消息开关应该为1");
            assertEquals(Integer.valueOf(0), response.getEntry().getMarketingMessageSwitch(), "营销消息开关应该为0");

            // 验证服务方法被调用
            verify(userSettingService, times(1)).getMessageSwitch();
        }
    }

    @Nested
    @DisplayName("语言设置测试")
    class LanguageSettingTest {

        @Test
        @DisplayName("获取语言列表 - 应该成功")
        void testGetLanguage_Success() {
            // 准备测试数据
            AppProperties.Setting zhLang = new AppProperties.Setting();
            zhLang.setType(LANGUAGE_ZH);
            zhLang.setContent("中文");
            AppProperties.Setting enLang = new AppProperties.Setting();
            enLang.setType(LANGUAGE_EN);
            enLang.setContent("English");
            List<AppProperties.Setting> langList = Arrays.asList(zhLang, enLang);

            // 模拟appProperties.getLang()方法
            when(appProperties.getLang()).thenReturn(langList);

            // 调用控制器方法
            R<List<AppLangVo>> response = userSettingController.language();

            // 验证结果
            assertNotNull(response, "响应不应为null");
            assertTrue(response.isStatus(), "响应应该成功");
            assertNotNull(response.getEntry(), "响应数据不应为null");
            assertEquals(2, response.getEntry().size(), "语言列表应该有两个元素");
            assertEquals(LANGUAGE_ZH, response.getEntry().get(0).getType(), "第一个语言类型应该是zh");
            assertEquals(LANGUAGE_EN, response.getEntry().get(1).getType(), "第二个语言类型应该是en");

            // 验证服务方法被调用
            verify(appProperties, times(1)).getLang();
        }

        @Test
        @DisplayName("设置用户语言 - 应该成功")
        void testSetLanguage_Success() {
            try (MockedStatic<StpUtil> stpUtilMocked = Mockito.mockStatic(StpUtil.class)) {
                // 模拟StpUtil.getLoginIdAsLong()返回用户ID
                stpUtilMocked.when(StpUtil::getLoginIdAsLong).thenReturn(USER_ID);

                // 模拟UserSettingService.setUserLanguage方法
                doNothing().when(userSettingService).setUserLanguage(USER_ID, LANGUAGE_ZH);

                // 调用控制器方法
                userSettingController.setLanguage(LANGUAGE_ZH);

                // 验证服务方法被调用
                verify(userSettingService, times(1)).setUserLanguage(USER_ID, LANGUAGE_ZH);
            }
        }
    }

    @Nested
    @DisplayName("推送Token设置测试")
    class PushTokenTest {

        @Test
        @DisplayName("设置推送Token - 应该成功")
        void testSetPushToken_Success() {
            // 准备测试数据
            SingleInfoReq<String> req = new SingleInfoReq<>();
            req.setReq(PUSH_TOKEN);

            // 模拟UserSettingService.setPushToken方法
            doNothing().when(userSettingService).setPushToken(PUSH_TOKEN);

            // 调用控制器方法
            userSettingController.setPushToken(req);

            // 验证服务方法被调用
            verify(userSettingService, times(1)).setPushToken(PUSH_TOKEN);
        }
    }

    @Nested
    @DisplayName("紧急联系人设置测试")
    class EmergencyContactTest {

        @Test
        @DisplayName("创建紧急联系人 - 应该成功")
        void testCreateEmergencyContact_Success() {
            // 准备测试数据
            DeviceAlarmDto requestDto = createMockDeviceAlarmDto();
            requestDto.setUserId(null); // 模拟前端传入时不包含用户ID

            try (MockedStatic<StpUtil> stpUtilMocked = Mockito.mockStatic(StpUtil.class);
                 MockedStatic<LocaleContextHolder> localeContextHolderMocked = Mockito.mockStatic(LocaleContextHolder.class)) {

                // 模拟StpUtil.getLoginIdAsLong()返回用户ID
                stpUtilMocked.when(StpUtil::getLoginIdAsLong).thenReturn(USER_ID);

                // 模拟LocaleContextHolder.getLocale()返回中文环境
                localeContextHolderMocked.when(LocaleContextHolder::getLocale).thenReturn(Locale.CHINESE);

                // 模拟DeviceAlarmService.create方法
                when(deviceAlarmService.create(any(DeviceAlarmDto.class))).thenReturn(true);

                // 调用控制器方法
                Boolean response = userSettingController.create(requestDto);

                // 验证结果
                assertTrue(response, "创建结果应该为true");
                assertEquals(USER_ID, requestDto.getUserId(), "用户ID应该被设置");
                assertEquals("zh", requestDto.getLanguage(), "语言应该被设置为zh");

                // 验证服务方法被调用
                verify(deviceAlarmService, times(1)).create(requestDto);
            }
        }
    }

    // ================================ 辅助方法 ================================

    /**
     * 创建模拟的MessageSettingDto对象
     */
    private MessageSettingDto createMockMessageSettingDto() {
        MessageSettingDto dto = new MessageSettingDto();
        dto.setSystemMessageSwitch(1);
        dto.setDeviceMessageSwitch(1);
        dto.setMarketingMessageSwitch(0);
        return dto;
    }

    /**
     * 创建模拟的MessageSettingVo对象
     */
    private MessageSettingVo createMockMessageSettingVo() {
        MessageSettingVo vo = new MessageSettingVo();
        vo.setSystemMessageSwitch(1);
        vo.setDeviceMessageSwitch(1);
        vo.setMarketingMessageSwitch(0);
        return vo;
    }

    /**
     * 创建模拟的AppLangVo列表
     */
    private List<AppLangVo> createMockAppLangList() {
        List<AppLangVo> list = new ArrayList<>();

        AppLangVo zhVo = new AppLangVo();
        zhVo.setType(LANGUAGE_ZH);
        zhVo.setContent("中文");
        list.add(zhVo);

        AppLangVo enVo = new AppLangVo();
        enVo.setType(LANGUAGE_EN);
        enVo.setContent("English");
        list.add(enVo);

        return list;
    }

    /**
     * 创建模拟的DeviceAlarmDto对象
     */
    private DeviceAlarmDto createMockDeviceAlarmDto() {
        DeviceAlarmDto dto = new DeviceAlarmDto();
        dto.setId(1L);
        dto.setDeviceId(DEVICE_ID);
        dto.setUserId(USER_ID);
        dto.setFirstName("John");
        dto.setLastName("Doe");
        dto.setCountryCode("+1");
        dto.setTelNumber("1234567890");
        dto.setNotificationByCall(1);
        dto.setNotificationByMsg(1);
        dto.setLanguage(LANGUAGE_EN);
        return dto;
    }
}