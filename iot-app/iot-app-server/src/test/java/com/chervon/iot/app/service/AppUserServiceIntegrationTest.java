package com.chervon.iot.app.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.fail;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description
 */
@SpringBootTest
public class AppUserServiceIntegrationTest {
    @Autowired
    private AppUserService appUserService;


    @Test
    void deleteByEmail() {
        String email = "<EMAIL>";
        try {
            appUserService.deleteByEmail(email);
        } catch (Exception e) {
            // 记录异常信息
            fail("Test failed with exception: " + e.getMessage());
        }
    }
}
