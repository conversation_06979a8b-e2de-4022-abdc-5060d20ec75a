package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.iot.app.config.AppProperties;
import com.chervon.iot.app.domain.dto.device.*;
import com.chervon.iot.app.domain.vo.device.AddUserDeviceBindVo;
import com.chervon.iot.app.domain.vo.device.AppDeviceStatusVo;
import com.chervon.iot.app.domain.vo.device.AppDeviceVo;
import com.chervon.iot.app.service.AppDeviceService;
import com.chervon.technology.api.RemoteDeviceFaultService;
import com.chervon.technology.api.dto.DeviceEditDto;
import com.chervon.technology.api.vo.DeviceFaultVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * DeviceController单元测试类
 * 
 * 测试覆盖范围：
 * 1. 设备绑定相关接口（通过ID和SN绑定）
 * 2. 设备详情查询接口（通过ID和SN查询）
 * 3. 设备列表查询接口
 * 4. 设备状态查询接口
 * 5. 设备故障查询接口
 * 6. 设备解绑接口
 * 7. 设备排序接口
 * 8. 设备编辑接口
 * 9. 产品ID查询接口
 *
 * 
 * <AUTHOR> Assistant
 * @since 2025-05-29
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DeviceController单元测试")
class DeviceControllerTest {

    @Mock
    private AppDeviceService appDeviceService;

    @Mock
    private RemoteDeviceFaultService remoteDeviceFaultService;

    @Mock
    private AppProperties appProperties;

    @InjectMocks
    private DeviceController deviceController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    // 测试常量
    private static final String TEST_DEVICE_ID = "TEST_DEVICE_001";
    private static final String TEST_DEVICE_SN = "SN123456789";
    private static final String TEST_MAC_ADDRESS = "AA:BB:CC:DD:EE:FF";
    private static final Long TEST_PRODUCT_ID = 1001L;
    private static final Long TEST_PERIOD = 3600L;
    private static final String TEST_LANG = "zh-CN";
    private static final String TEST_APP_VERSION = "1.0.0";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(deviceController).build();
        objectMapper = new ObjectMapper();
    }

    @Nested
    @DisplayName("设备绑定测试")
    class DeviceBindTests {

        @Test
        @DisplayName("通过设备ID绑定设备 - 正常情况")
        void testBindByDeviceId_Success() {
            // Given
            AppUserDeviceIdDto requestDto = new AppUserDeviceIdDto();
            requestDto.setDeviceId(TEST_DEVICE_ID);
            requestDto.setMac(TEST_MAC_ADDRESS);
            requestDto.setDeviceShare(false);

            AddUserDeviceBindVo expectedResponse = new AddUserDeviceBindVo();
            expectedResponse.setDeviceId(TEST_DEVICE_ID);
            expectedResponse.setIsDeviceInfoRegistered(true);

            when(appDeviceService.bindByDeviceId(any(AppUserDeviceIdDto.class)))
                    .thenReturn(expectedResponse);

            // When
            R<AddUserDeviceBindVo> result = deviceController.bind(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertEquals(expectedResponse, result.getEntry());
            verify(appDeviceService, times(1)).bindByDeviceId(requestDto);
        }

        @Test
        @DisplayName("通过设备SN绑定设备 - 正常情况")
        void testBindBySn_Success() {
            // Given
            AppUserDeviceSnDto requestDto = new AppUserDeviceSnDto();
            requestDto.setSn(TEST_DEVICE_SN);
            requestDto.setMac(TEST_MAC_ADDRESS);

            AddUserDeviceBindVo expectedResponse = new AddUserDeviceBindVo();
            expectedResponse.setDeviceId(TEST_DEVICE_ID);
            expectedResponse.setIsDeviceInfoRegistered(true);

            when(appDeviceService.bindBySn(any(AppUserDeviceSnDto.class)))
                    .thenReturn(expectedResponse);

            // When
            R<AddUserDeviceBindVo> result = deviceController.bindBySn(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertEquals(expectedResponse, result.getEntry());
            verify(appDeviceService, times(1)).bindBySn(requestDto);
        }

        @Test
        @DisplayName("通过设备SN绑定设备 - 异常情况")
        void testBindBySn_ServiceException() {
            // Given
            AppUserDeviceSnDto requestDto = new AppUserDeviceSnDto();
            requestDto.setSn(TEST_DEVICE_SN);

            when(appDeviceService.bindBySn(any(AppUserDeviceSnDto.class)))
                    .thenThrow(new RuntimeException("设备绑定失败"));

            // When & Then
            assertThrows(RuntimeException.class, () -> {
                deviceController.bindBySn(requestDto);
            });
        }
    }

    @Nested
    @DisplayName("设备详情查询测试")
    class DeviceDetailTests {

        @Test
        @DisplayName("通过ID获取设备详情 - 正常情况")
        void testGetDeviceDetail_Success() {
            // Given
            SingleInfoReq<String> request = new SingleInfoReq<>();
            request.setReq(TEST_DEVICE_ID);
            
            MockHttpServletRequest httpRequest = new MockHttpServletRequest();
            
            AppDeviceVo expectedDevice = createTestAppDeviceVo();
            
            when(appDeviceService.detail(eq(TEST_DEVICE_ID), any(HttpServletRequest.class)))
                    .thenReturn(expectedDevice);
            when(appProperties.getDeviceInfoPeriod()).thenReturn(TEST_PERIOD);

            // When
            R<AppDeviceVo> result = deviceController.detail(request, httpRequest);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(TEST_DEVICE_ID, result.getEntry().getDeviceId());
            assertEquals(TEST_PERIOD, result.getEntry().getPeriod());
            verify(appDeviceService, times(1)).detail(TEST_DEVICE_ID, httpRequest);
        }

        @Test
        @DisplayName("通过SN获取设备详情 - 正常情况")
        void testGetDeviceDetailBySn_Success() {
            // Given
            SingleInfoReq<String> request = new SingleInfoReq<>();
            request.setReq(TEST_DEVICE_SN);
            
            MockHttpServletRequest httpRequest = new MockHttpServletRequest();
            
            AppDeviceVo expectedDevice = createTestAppDeviceVo();
            
            when(appDeviceService.detailBySn(eq(TEST_DEVICE_SN), any(HttpServletRequest.class)))
                    .thenReturn(expectedDevice);
            when(appProperties.getDeviceInfoPeriod()).thenReturn(TEST_PERIOD);

            // When
            R<AppDeviceVo> result = deviceController.detailBySn(request, httpRequest);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(TEST_PERIOD, result.getEntry().getPeriod());
            verify(appDeviceService, times(1)).detailBySn(TEST_DEVICE_SN, httpRequest);
        }

        @Test
        @DisplayName("通过ID获取设备详情 - 设备不存在")
        void testGetDeviceDetail_DeviceNotFound() {
            // Given
            SingleInfoReq<String> request = new SingleInfoReq<>();
            request.setReq("NON_EXISTENT_DEVICE");
            
            MockHttpServletRequest httpRequest = new MockHttpServletRequest();
            
            when(appDeviceService.detail(eq("NON_EXISTENT_DEVICE"), any(HttpServletRequest.class)))
                    .thenThrow(new RuntimeException("设备不存在"));

            // When & Then
            assertThrows(RuntimeException.class, () -> {
                deviceController.detail(request, httpRequest);
            });
        }
    }

    @Nested
    @DisplayName("设备列表查询测试")
    class DeviceListTests {

        @Test
        @DisplayName("获取设备列表 - 正常情况")
        void testGetDeviceList_Success() {
            // Given
            MockHttpServletRequest httpRequest = new MockHttpServletRequest();
            List<AppDeviceVo> expectedDevices = Arrays.asList(
                    createTestAppDeviceVo(),
                    createTestAppDeviceVo()
            );
            
            when(appDeviceService.listBoundDevices(TEST_LANG, TEST_APP_VERSION))
                    .thenReturn(expectedDevices);

            // When
            R<List<AppDeviceVo>> result = deviceController.list(TEST_LANG, TEST_APP_VERSION, httpRequest);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(2, result.getEntry().size());
            verify(appDeviceService, times(1)).listBoundDevices(TEST_LANG, TEST_APP_VERSION);
        }

        @Test
        @DisplayName("获取设备列表 - 空列表")
        void testGetDeviceList_EmptyList() {
            // Given
            MockHttpServletRequest httpRequest = new MockHttpServletRequest();
            List<AppDeviceVo> emptyList = Collections.emptyList();
            
            when(appDeviceService.listBoundDevices(TEST_LANG, TEST_APP_VERSION))
                    .thenReturn(emptyList);

            // When
            R<List<AppDeviceVo>> result = deviceController.list(TEST_LANG, TEST_APP_VERSION, httpRequest);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertTrue(result.getEntry().isEmpty());
        }

        @Test
        @DisplayName("获取调试设备列表 - 正常情况")
        void testGetDebugDeviceList_Success() {
            // Given
            List<AppDeviceVo> expectedDevices = Arrays.asList(createTestAppDeviceVo());
            
            when(appDeviceService.listBoundDebugDevices()).thenReturn(expectedDevices);

            // When
            R<List<AppDeviceVo>> result = deviceController.debugList();

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(1, result.getEntry().size());
            verify(appDeviceService, times(1)).listBoundDebugDevices();
        }
    }

    @Nested
    @DisplayName("设备状态查询测试")
    class DeviceStatusTests {

        @Test
        @DisplayName("获取设备状态 - 正常情况")
        void testGetDeviceStatus_Success() {
            // Given
            AppDeviceStatusDto requestDto = new AppDeviceStatusDto();
            requestDto.setDeviceId(TEST_DEVICE_ID);
            
            AppDeviceStatusVo expectedStatus = new AppDeviceStatusVo();
            expectedStatus.setDeviceStatus(1);
            expectedStatus.setDeviceCodeStatus(1);
            expectedStatus.setIsOnline(1);
            
            when(appDeviceService.getStatus(any(AppDeviceStatusDto.class)))
                    .thenReturn(expectedStatus);

            // When
            R<AppDeviceStatusVo> result = deviceController.getStatus(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(Integer.valueOf(1), result.getEntry().getIsOnline());
            assertEquals(Integer.valueOf(1), result.getEntry().getDeviceStatus());
            verify(appDeviceService, times(1)).getStatus(requestDto);
        }

        @Test
        @DisplayName("获取设备状态 - 设备离线")
        void testGetDeviceStatus_DeviceOffline() {
            // Given
            AppDeviceStatusDto requestDto = new AppDeviceStatusDto();
            requestDto.setDeviceId(TEST_DEVICE_ID);
            
            AppDeviceStatusVo expectedStatus = new AppDeviceStatusVo();
            expectedStatus.setDeviceStatus(0);
            expectedStatus.setDeviceCodeStatus(0);
            expectedStatus.setIsOnline(0);
            
            when(appDeviceService.getStatus(any(AppDeviceStatusDto.class)))
                    .thenReturn(expectedStatus);

            // When
            R<AppDeviceStatusVo> result = deviceController.getStatus(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(Integer.valueOf(0), result.getEntry().getIsOnline());
        }
    }

    @Nested
    @DisplayName("设备故障查询测试")
    class DeviceFaultTests {

        @Test
        @DisplayName("获取设备故障列表 - 正常情况")
        void testGetDeviceFaultList_Success() {
            // Given
            DeviceFaultQueryDto requestDto = new DeviceFaultQueryDto();
            requestDto.setDeviceId(TEST_DEVICE_ID);
            
            List<DeviceFaultVo> expectedFaults = Arrays.asList(
                    createTestDeviceFaultVo("故障1"),
                    createTestDeviceFaultVo("故障2")
            );
            
            when(remoteDeviceFaultService.getDeviceFaultList(TEST_DEVICE_ID))
                    .thenReturn(expectedFaults);

            // When
            R<List<DeviceFaultVo>> result = deviceController.getDeviceFaultList(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(2, result.getEntry().size());
            verify(remoteDeviceFaultService, times(1)).getDeviceFaultList(TEST_DEVICE_ID);
        }

        @Test
        @DisplayName("获取设备故障列表 - 参数为空")
        void testGetDeviceFaultList_NullRequest() {
            // When & Then
            assertThrows(Exception.class, () -> {
                deviceController.getDeviceFaultList(null);
            });
        }

        @Test
        @DisplayName("获取设备故障列表 - 设备ID为空")
        void testGetDeviceFaultList_NullDeviceId() {
            // Given
            DeviceFaultQueryDto requestDto = new DeviceFaultQueryDto();
            requestDto.setDeviceId(null);

            // When & Then
            assertThrows(Exception.class, () -> {
                deviceController.getDeviceFaultList(requestDto);
            });
        }

        @Test
        @DisplayName("获取设备故障消息 - 正常情况")
        void testGetDeviceFaultMessage_Success() {
            // Given
            DeviceFaultQueryDto requestDto = new DeviceFaultQueryDto();
            requestDto.setDeviceId(TEST_DEVICE_ID);
            
            List<DeviceFaultVo> expectedMessages = Arrays.asList(createTestDeviceFaultVo("消息1"));
            
            when(remoteDeviceFaultService.getDeviceFaultMessage(TEST_DEVICE_ID))
                    .thenReturn(expectedMessages);

            // When
            R<List<DeviceFaultVo>> result = deviceController.getDeviceFaultMessage(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(1, result.getEntry().size());
            verify(remoteDeviceFaultService, times(1)).getDeviceFaultMessage(TEST_DEVICE_ID);
        }
    }

    @Nested
    @DisplayName("设备操作测试")
    class DeviceOperationTests {

        @Test
        @DisplayName("解绑设备 - 正常情况")
        void testUnbindDevice_Success() {
            // Given
            AppUserDeviceUnbindDto requestDto = new AppUserDeviceUnbindDto();
            requestDto.setDeviceId(TEST_DEVICE_ID);
            
            doNothing().when(appDeviceService).unbind(any(AppUserDeviceUnbindDto.class));

            // When
            R<?> result = deviceController.delete(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            verify(appDeviceService, times(1)).unbind(requestDto);
        }

        @Test
        @DisplayName("解绑设备 - 服务异常")
        void testUnbindDevice_ServiceException() {
            // Given
            AppUserDeviceUnbindDto requestDto = new AppUserDeviceUnbindDto();
            requestDto.setDeviceId(TEST_DEVICE_ID);
            
            doThrow(new RuntimeException("解绑失败")).when(appDeviceService).unbind(any(AppUserDeviceUnbindDto.class));

            // When & Then
            assertThrows(RuntimeException.class, () -> {
                deviceController.delete(requestDto);
            });
        }

        @Test
        @DisplayName("编辑设备 - 正常情况")
        void testEditDevice_Success() {
            // Given
            DeviceEditDto requestDto = new DeviceEditDto();
            requestDto.setDeviceId(TEST_DEVICE_ID);
            // 由于DeviceEditDto的具体字段不确定，这里只设置deviceId
            
            doNothing().when(appDeviceService).edit(any(DeviceEditDto.class));

            // When
            R<?> result = deviceController.edit(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            verify(appDeviceService, times(1)).edit(requestDto);
        }
    }

    @Nested
    @DisplayName("产品ID查询测试")
    class ProductIdTests {

        @Test
        @DisplayName("通过SN获取产品ID - 正常情况")
        void testGetProductIdBySn_Success() {
            // Given
            SingleInfoReq<String> request = new SingleInfoReq<>();
            request.setReq(TEST_DEVICE_SN);
            
            when(appDeviceService.getProductIdBySn(TEST_DEVICE_SN)).thenReturn(TEST_PRODUCT_ID);

            // When
            R<SingleInfoResp<Long>> result = deviceController.getProductIdBySn(request);

            // Then
            assertNotNull(result);
            assertTrue(result.isStatus());
            assertNotNull(result.getEntry());
            assertEquals(TEST_PRODUCT_ID, result.getEntry().getInfo());
            verify(appDeviceService, times(1)).getProductIdBySn(TEST_DEVICE_SN);
        }

        @Test
        @DisplayName("通过SN获取产品ID - SN不存在")
        void testGetProductIdBySn_SnNotFound() {
            // Given
            SingleInfoReq<String> request = new SingleInfoReq<>();
            request.setReq("INVALID_SN");
            
            when(appDeviceService.getProductIdBySn("INVALID_SN"))
                    .thenThrow(new RuntimeException("SN不存在"));

            // When & Then
            assertThrows(RuntimeException.class, () -> {
                deviceController.getProductIdBySn(request);
            });
        }

        @Test
        @DisplayName("通过SN获取产品ID - 边界值测试（空SN）")
        void testGetProductIdBySn_EmptySn() {
            // Given
            SingleInfoReq<String> request = new SingleInfoReq<>();
            request.setReq("");
            
            when(appDeviceService.getProductIdBySn(""))
                    .thenThrow(new IllegalArgumentException("SN不能为空"));

            // When & Then
            assertThrows(IllegalArgumentException.class, () -> {
                deviceController.getProductIdBySn(request);
            });
        }
    }

    /**
     * 创建测试用的AppDeviceVo对象
     */
    private AppDeviceVo createTestAppDeviceVo() {
        AppDeviceVo device = new AppDeviceVo();
        device.setProductId(TEST_PRODUCT_ID);
        device.setDeviceId(TEST_DEVICE_ID);
        device.setSn(TEST_DEVICE_SN);
        device.setDeviceName("测试设备");
        device.setNickName("我的设备");
        device.setVersion("1.0.0");
        device.setIsOnline(1);
        device.setStatus(1);
        device.setDeviceCodeStatus(1);
        device.setDeviceIcon("device_icon.png");
        device.setCommunicateMode("wifi");
        device.setProductType("directConnectedDevice");
        device.setMac(TEST_MAC_ADDRESS);
        device.setSort(1);
        device.setUserIdList(Arrays.asList(1L, 2L));
        device.setBindTime(LocalDateTime.now());
        device.setInfoStatus(1);
        device.setRnBundleName("TestBundle");
        device.setAssemblySnList(Arrays.asList("ASM001", "ASM002"));
        device.setCommodityModel("TEST_MODEL");
        device.setPeriod(TEST_PERIOD);
        device.setAccessoryQuantity(5L);
        device.setOfflineDays(0L);
        device.setProductName("测试产品");
        device.setQuestionTemplate("commonTemplate");
        device.setShareType(1);
        device.setModel("TEST_MODEL_V1");
        device.setCategoryId("CAT001");
        device.setCategoryName("测试品类");
        return device;
    }

    /**
     * 创建测试用的DeviceFaultVo对象
     */
    private DeviceFaultVo createTestDeviceFaultVo(String faultName) {
        DeviceFaultVo fault = new DeviceFaultVo();
        // 由于DeviceFaultVo的具体字段不确定，这里简化处理
        // 实际使用时需要根据真实的DeviceFaultVo字段进行设置
        return fault;
    }
}