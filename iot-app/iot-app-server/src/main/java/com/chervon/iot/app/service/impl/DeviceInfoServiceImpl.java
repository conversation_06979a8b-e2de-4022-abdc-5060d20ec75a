package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.config.IotAppCommonConstant;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dataobject.DeviceInfo;
import com.chervon.iot.app.domain.dataobject.DeviceInfoReceipt;
import com.chervon.iot.app.domain.dataobject.DeviceInfoSyncTemp;
import com.chervon.iot.app.domain.dataobject.promotion.RecommendedAccessory;
import com.chervon.iot.app.domain.dataobject.*;
import com.chervon.iot.app.domain.dto.device.DeviceInfoAddDto;
import com.chervon.iot.app.domain.dto.device.DeviceInfoDetailDto;
import com.chervon.iot.app.domain.dto.device.UploadReceiptDto;
import com.chervon.iot.app.domain.enums.ApplyWithEnum;
import com.chervon.iot.app.domain.enums.ProductTypeEnum;
import com.chervon.iot.app.domain.enums.PurchasePlaceEnum;
import com.chervon.iot.app.domain.enums.ReceiptInformationStatusEnum;
import com.chervon.iot.app.domain.vo.PreSignedUrlVo;
import com.chervon.iot.app.domain.vo.device.DeviceInfoVo;
import com.chervon.iot.app.domain.vo.device.PartsInfoVo;
import com.chervon.iot.app.domain.vo.dict.DictNode;
import com.chervon.iot.app.domain.vo.dict.DictVo;
import com.chervon.iot.app.mapper.DeviceInfoMapper;
import com.chervon.iot.app.mapper.DeviceInfoSyncFailMapper;
import com.chervon.iot.app.mapper.DeviceInfoSyncTempMapper;
import com.chervon.iot.app.service.*;
import com.chervon.iot.app.util.DateUtils;
import com.chervon.operation.api.RemotePartsService;
import com.chervon.operation.api.dto.RecommendPartsDto;
import com.chervon.technology.api.RemoteDeviceCodeService;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.RemoteProductService;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.DeviceBindBo;
import com.chervon.technology.api.vo.DeviceCodeRpcVo;
import com.chervon.technology.api.vo.DeviceRpcVo;
import com.chervon.technology.api.vo.ProductRpcVo;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.UserVo;
import com.chervon.usercenter.api.vo.sf.SfWarrantyRecord;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-31 20:19
 **/
@Service
@Slf4j
public class DeviceInfoServiceImpl extends ServiceImpl<DeviceInfoMapper, DeviceInfo> implements DeviceInfoService {
    private static final int SN_LEN_LIMIT = 128;
    private static final int MIN_PURCHASE_YEAR = 2000;

    @Autowired
    private S3Util s3Util;
    @Autowired
    @Lazy
    private DeviceInfoAsyncService deviceInfoAsyncService;
    @Resource
    private DeviceInfoReceiptService deviceInfoReceiptService;
    @Resource
    private DeviceWarrantyQuestionnaireService deviceWarrantyQuestionnaireService;
    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @Autowired
    private AwsProperties awsProperties;
    @Autowired
    private DeviceInfoSyncFailMapper deviceInfoSyncFailMapper;
    @Autowired
    private DeviceInfoSyncTempMapper deviceInfoSyncTempMapper;
    @Autowired
    private AppDeviceService appDeviceService;
    @DubboReference
    private RemoteDeviceCodeService remoteDeviceCodeService;
    @DubboReference
    private UserQueryService userQueryService;
    @Value("${sf.direction}")
    private String direction;
    @Autowired
    private DictService dictService;
    @Autowired
    private RecommendAccessoryService recommendAccessoryService;
    @DubboReference
    private RemotePartsService remotePartsService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public PartsInfoVo add(DeviceInfoAddDto dto) {
        String sfSn = getSn(dto);
        if (ifDeviceAlreadyRegistered(dto.getDeviceId())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_ALREADY_REGISTERED, dto.getDeviceId());
        }
        DeviceInfo deviceInfo = ConvertUtil.convert(dto, DeviceInfo.class);
        LocalDateTime localDateTime = checkAndConvertPurchaseTime(dto.getPurchaseTime());
        checkSnPurchaseTime(sfSn, localDateTime);
        deviceInfo.setPurchaseTime(localDateTime);
        // 截止时间默认为3年
        LocalDateTime expireTime = localDateTime.plusYears(CommonConstant.THREE);
        // 根据是否填写维保问卷设置维保到期时间(如果填过问卷额外加2年)
        deviceInfo.setExpireTime((Objects.equals(dto.getIfCheckedWarranty(), CommonConstant.ONE)) ?
                expireTime.plusYears(CommonConstant.TWO) : expireTime);
        deviceInfo.setLastSyncTime(LocalDateTime.now());
        deviceInfo.setSourceCode(CommonConstant.ZERO);
        this.save(deviceInfo);
        registerKitSn(dto, deviceInfo, sfSn);
        bindMoreDevice(dto.getDeviceId(), sfSn);

        // 如果收据文件Key列表不为空
        if (!CollectionUtils.isEmpty(dto.getReceiptFileKey())) {
            List<DeviceInfoReceipt> deviceInfoReceiptList = new ArrayList<>();
            for (String receiptFileKey : dto.getReceiptFileKey()) {
                DeviceInfoReceipt deviceInfoReceipt = new DeviceInfoReceipt();
                deviceInfoReceipt.setDeviceId(dto.getDeviceId());
                deviceInfoReceipt.setReceiptFileKey(receiptFileKey);
                deviceInfoReceiptList.add(deviceInfoReceipt);
            }
            deviceInfoReceiptService.saveBatch(deviceInfoReceiptList);
        }
        // 异步注册设备质保到SaleForce
        // 暂时不异步
        deviceInfoAsyncService.registerToSaleForce(deviceInfo, dto, sfSn, StpUtil.getLoginIdAsLong());
        saveSyncTemp(sfSn, dto.getKitSnList());

        /**
         * 用户手动输入SN不为空，更新设备表SN
         */
        if (StringUtils.isNotBlank(dto.getSn())) {
            remoteDeviceManageService.updateSnByDeviceId(dto.getDeviceId(), dto.getSn());
        }

        // 注册完质保后，获取推荐配件信息
        try {
            String snCode = getSnCodeFromSn(dto.getSn());
            RecommendedAccessory recommendedAccessory = recommendAccessoryService.selectAccessoryBySnCode(snCode);

            if (recommendedAccessory == null) {
                log.info("No accessories to recommend!");
                return null;
            }
            RecommendPartsDto partsDto = remotePartsService.getRecommendParts(recommendedAccessory.getRecommendedAccessory());
            if (partsDto == null) {
                log.warn("Accessories information is not maintained!");
                return null;
            }
            return buildPartsInfoVo(partsDto);
        } catch (Exception e) {
            log.error("Failed to get recommended accessories: ", e);
            return null;
        }
    }

    // 抽取构建PartsInfoVo的方法
    private PartsInfoVo buildPartsInfoVo(RecommendPartsDto partsDto) {
        PartsInfoVo partsInfoVo = new PartsInfoVo();
        partsInfoVo.setPartId(partsDto.getPartId());
        partsInfoVo.setPartModel(partsDto.getPartModel());
        partsInfoVo.setPartName(partsDto.getPartName());
        partsInfoVo.setImageUrl(partsDto.getPartIconUrl());
        partsInfoVo.setShopUrl(partsDto.getPartShopUrl());
        return partsInfoVo;
    }

    /**
     * 检查并转换购买时间
     *
     * @param purchaseTime
     * @return
     */
    private LocalDateTime checkAndConvertPurchaseTime(Long purchaseTime) {
        if (DateUtils.localDateIsAfter(DateUtils.timestamp2LocalDate(purchaseTime), LocalDate.now())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.DEVICE_INFO_PURCHASETIME_EXCEED, purchaseTime);
        }
        return DateUtils.timestamp2LocalDateTime(purchaseTime);
    }


    /**
     * 获取sn
     */
    private String getSn(DeviceInfoAddDto dto) {
        String deviceId = dto.getDeviceId();
        String sn = dto.getSn();

        DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetail(deviceId);
        if (Objects.nonNull(deviceCodeRpcVo) && StringUtils.isNotBlank(deviceCodeRpcVo.getSn())) {
            return deviceCodeRpcVo.getSn();
        } else {
            if (StringUtils.isNotBlank(sn)) {
                //用户手动输入sn
                String manualSn = checkAndConvertSn(sn);
                if (!Objects.equals(getSnCodeFromSn(manualSn), deviceId.substring(1, 5))) {
                    throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_REGISTER_FAILED, deviceId);
                }
                //根据SN码查询多码关系, 比较deviceId
                DeviceCodeRpcVo deviceCodeDetailBySn = remoteDeviceCodeService.deviceCodeDetailBySn(manualSn);
                if (deviceCodeDetailBySn != null && !deviceId.equals(deviceCodeDetailBySn.getDeviceId())) {
                    throw ExceptionMessageUtil.getException(AppErrorCode.DEVICE_INFO_SN_ERROR, sn);
                }
                DeviceRpcVo deviceById = remoteDeviceManageService.deviceDetail(deviceId);
                //查询设备表是否有数据
                if (Objects.isNull(deviceById)) {
                    throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_REGISTER_FAILED, deviceId);
                }
                if (StringUtils.isEmpty(deviceById.getSn()) || !deviceById.getSn().equals(manualSn)) {
                    DeviceRpcVo deviceBySn = remoteDeviceManageService.deviceDetailBySn(manualSn);
                    if (deviceBySn != null && !deviceId.equals(deviceBySn.getDeviceId())) {
                        throw ExceptionMessageUtil.getException(AppErrorCode.DEVICE_INFO_SN_ERROR, sn);
                    }
                }
                return manualSn;
            }
        }
        throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_REGISTER_FAILED, deviceId);
    }

    private String checkAndConvertSn(String sn) {
        //去空格，转大写
        sn = sn.trim().toUpperCase();
        if (!sn.matches(IotAppCommonConstant.DEVICE_CODE_CHECK) &&
                !sn.matches(IotAppCommonConstant.NO_IOT_DEVICE_CODE_CHECK)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR, sn);
        }
        return sn;
    }

    /**
     * 校验SN码和购买时间
     */
    private void checkSnPurchaseTime(String sn, LocalDateTime purchaseTime) {
        if (IotAppCommonConstant.NA.equals(direction)) {
            return;
        }
        int year = Integer.parseInt("20" + sn.substring(5, 7));
        int weeks = Integer.parseInt(sn.substring(7, 9));
        LocalDate date = LocalDate.of(year, 1, 1).plusWeeks(weeks);
        if (DateUtils.localDateIsAfter(date, purchaseTime.toLocalDate())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.DEVICE_INFO_PURCHASETIME_SN_ERROR, sn);
        }
    }

    private String getSnCodeFromSn(String sn) {
        if (sn.matches(IotAppCommonConstant.DEVICE_CODE_CHECK)) {
            // 15位多码
            return sn.substring(1, 5);
        } else if (sn.matches(IotAppCommonConstant.NO_IOT_DEVICE_CODE_CHECK)) {
            // 16位，R开头的非IOT设备多码
            return sn.substring(2, 6);
        } else {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR, sn);
        }
    }

    /**
     * 注册kitSn
     */
    private void registerKitSn(DeviceInfoAddDto dto, DeviceInfo deviceInfo, String sn) {
        List<String> kitSnList = dto.getKitSnList();
        if(CollectionUtils.isEmpty(kitSnList)) {
            return;
        }
        String parentDeviceId = deviceInfo.getDeviceId();
        Set<String> kitSnSet = Sets.newHashSet();
        for(String kitSn : kitSnList) {
            if(StringUtils.isEmpty(kitSn)) {
                continue;
            }
            if(kitSnSet.contains(kitSn) || kitSn.equals(sn)) {
                continue;
            }
            checkSnPurchaseTime(kitSn, deviceInfo.getPurchaseTime());
            String deviceId = bindSnOfCrm(kitSn, StpUtil.getLoginIdAsLong());
            if (StringUtils.isEmpty(deviceId)) {
                throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_NOT_EXIST, kitSn);
            }
            if (ifDeviceAlreadyRegistered(deviceId)) {
                //检查质保是否存在
                throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_ALREADY_REGISTERED, deviceId);
            }
            deviceInfo.setId(null);
            deviceInfo.setDeviceId(deviceId);
            deviceInfo.setParentDeviceId(parentDeviceId);
            this.save(deviceInfo);
            kitSnSet.add(kitSn);
        }
        deviceInfo.setDeviceId(parentDeviceId);
        deviceInfo.setParentDeviceId(null);
        //将重复的SN过滤掉，避免再去DeviceInfoAsyncService修改
        dto.setKitSnList(Lists.newArrayList(kitSnSet));
    }

    /**
     * 绑定用户设备(注册更多设备)
     *
     * @param deviceId 设备Id
     * @param sn       设备SN码
     */
    private void bindMoreDevice(String deviceId, String sn) {
        AppUserDevice existsOne = appDeviceService.getOne(new LambdaQueryWrapper<AppUserDevice>()
                .select(AppUserDevice::getId)
                .eq(AppUserDevice::getDeviceId, deviceId).eq(AppUserDevice::getUserId, StpUtil.getLoginIdAsLong())
                .last("limit 1"));
        if (existsOne != null) {
            return;
        }
        bindSnOfCrm(sn, StpUtil.getLoginIdAsLong());
    }

    @Override
    public DeviceInfo getByDeviceId(String deviceId) {
        LambdaQueryWrapper<DeviceInfo> deviceInfoLambdaQueryWrapper = new LambdaQueryWrapper<DeviceInfo>()
                .eq(DeviceInfo::getDeviceId, deviceId)
                .last("limit 1");
        return this.getOne(deviceInfoLambdaQueryWrapper);
    }

    private DeviceInfoVo changVo(DeviceInfo deviceInfo) {
        // 从配置中心获取字典对应多语言文案结果
        List<String> dictNames = Arrays.asList(IotAppCommonConstant.PURCHASE_PLACE, IotAppCommonConstant.APPLY_WITH);
        Map<String, DictVo> dictMap = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), dictNames).stream().collect(Collectors.toMap(DictVo::getDictName, Function.identity()));
        Map<String, Map<String, String>> quickMap = new HashMap<>(dictMap.size());
        dictMap.forEach((key, value) -> {
            Map<String, String> contentMap = new HashMap<>(dictMap.get(key).getNodes().size());
            dictMap.get(key).getNodes().forEach(a -> contentMap.put(a.getLabel(), a.getDescription()));
            quickMap.put(key, contentMap);
        });

        // Vo的拼接
        DeviceInfoVo result = new DeviceInfoVo();
        result.setApplyWith(quickMap.get(IotAppCommonConstant.APPLY_WITH).get(deviceInfo.getApplyWith()));
        if (deviceInfo.getPurchasePlace().equals(IotAppCommonConstant.OTHER)) {
            result.setPurchasePlace(deviceInfo.getPurchasePlaceOther());
        } else {
            result.setPurchasePlace(quickMap.get(IotAppCommonConstant.PURCHASE_PLACE)
                    .get(deviceInfo.getPurchasePlace()));
        }
        result.setPurchaseTime(deviceInfo.getPurchaseTime());
        //获取注册质保收据信息
        List<String> receptFileUrls = deviceInfoReceiptService.listByDeviceId(deviceInfo.getDeviceId())
                .stream()
                .map(x -> UrlUtil.completeUrl(awsProperties.getReceiptBucket().getCdnHost(), x.getReceiptFileKey()))
                .collect(Collectors.toList());
        result.setReceiptFileUrls(receptFileUrls);
        return result;
    }

    @Override
    public DeviceInfoVo detail(DeviceInfoDetailDto dto) {
        DeviceInfo one = this.getByDeviceId(dto.getDeviceId());
        if (null == one) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_NOT_REGISTERED, dto.getDeviceId());
        }
        return changVo(one);
    }

    @Override
    public Boolean ifDeviceAlreadyRegistered(String deviceId) {
        LambdaQueryWrapper<DeviceInfo> deviceInfoWrapper = new LambdaQueryWrapper<DeviceInfo>()
                .eq(DeviceInfo::getDeviceId, deviceId)
                .last("limit 1");
        return this.getOne(deviceInfoWrapper) != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PreSignedUrlVo uploadReceipt(UploadReceiptDto dto) {
        String deviceId = dto.getDeviceId();
        String fileName = dto.getFileName();
        String key = IotAppCommonConstant.RECEIPT_PATH + IotAppCommonConstant.FILE_SPLIT_CODE;
        if (null != deviceId) {
            key = key + deviceId + IotAppCommonConstant.FILE_SPLIT_CODE + fileName;
        } else {
            key = key + fileName;
        }
        String preSignedUrl = s3Util.getPreSignedPutPrivateUrl(awsProperties.getReceiptBucket().getName(), key);
        PreSignedUrlVo preSignedUrlVo = new PreSignedUrlVo();
        preSignedUrlVo.setKey(key);
        preSignedUrlVo.setPreSignedUrl(preSignedUrl);
        return preSignedUrlVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByDeviceId(DeviceInfoDetailDto deviceInfoDetailDto) {
        DeviceInfo deviceInfo = this.getByDeviceId(deviceInfoDetailDto.getDeviceId());
        if (null == deviceInfo) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_NOT_REGISTERED1, deviceInfoDetailDto.getDeviceId());
        }
        LambdaQueryWrapper<DeviceInfoReceipt> deviceInfoReceiptLambdaQueryWrapper = new LambdaQueryWrapper<DeviceInfoReceipt>()
                .eq(DeviceInfoReceipt::getDeviceId, deviceInfoDetailDto.getDeviceId());
        deviceInfoReceiptService.remove(deviceInfoReceiptLambdaQueryWrapper);
        this.removeById(deviceInfo);
    }

    @Override
    public boolean deviceIsRegistered(String deviceId) {
        long count = this.count(new LambdaQueryWrapper<DeviceInfo>().eq(DeviceInfo::getDeviceId, deviceId));
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeDeviceInfoBySn(String sn) {
        //sn校验
        sn = checkAndConvertSn(sn);
        //查询设备信息
        DeviceRpcVo deviceRpcVo = remoteDeviceManageService.deviceDetailBySn(sn);
        if (Objects.isNull(deviceRpcVo)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_NOT_EXIST, sn);
        }
        String deviceId = deviceRpcVo.getDeviceId();
        //判断该设备是否注册过
        if (!ifDeviceAlreadyRegistered(deviceId)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_NOT_REGISTERED, deviceId);
        }
        //删除质保表
        removeByDeviceId(deviceId);
        //删除质保关联收据表
        deviceInfoReceiptService.removeByDeviceId(deviceId);
        //删除质保关联问卷表
        deviceWarrantyQuestionnaireService.removeByDeviceId(deviceId);
        //删除缓存
        deviceInfoSyncTempMapper.delete(new LambdaQueryWrapper<DeviceInfoSyncTemp>().eq(DeviceInfoSyncTemp::getSn, sn));
        //修正设备表SN
        revisedDeviceSn(sn, deviceId);
    }

    private void revisedDeviceSn(String sn, String deviceId) {
        //判断设备表SN是否需要修正
        DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetail(deviceId);
        if (Objects.nonNull(deviceCodeRpcVo)) {
            if (!Objects.equals(sn, deviceCodeRpcVo.getSn())) {
                remoteDeviceManageService.updateSnByDeviceId(deviceId, deviceCodeRpcVo.getSn());
            }
        } else {
            remoteDeviceManageService.updateSnByDeviceId(deviceId, null);
        }
    }

    @Override
    public void removeByDeviceId(String deviceId) {
        remove(Wrappers.<DeviceInfo>lambdaQuery().eq(DeviceInfo::getDeviceId, deviceId));
    }

    @Override
    public void validateSn(String sn) {
        checkAndConvertSn(sn);
        String deviceId = null;
        DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetailBySn(sn);
        if(deviceCodeRpcVo==null || StringUtils.isEmpty(deviceCodeRpcVo.getDeviceId())) {
            DeviceRpcVo deviceBySn = remoteDeviceManageService.deviceDetailBySn(sn);
            if(deviceBySn != null) {
                deviceId = deviceBySn.getDeviceId();
            }
        } else {
            deviceId = deviceCodeRpcVo.getDeviceId();
        }
        if(StringUtils.isEmpty(deviceId)) {
            return;
        }
        if(deviceIsRegistered(deviceId)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_ALREADY_REGISTERED, sn);
        }
    }

    @Override
    public void syncDeviceFromCrm(List<SfWarrantyRecord> sfWarrantyRecords) {
        if (CollectionUtils.isEmpty(sfWarrantyRecords)) {
            return;
        }

        List<DeviceInfo> deviceInfoList = Lists.newArrayList();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (SfWarrantyRecord sfWarrantyRecord : sfWarrantyRecords) {
            try {
                if (StringUtils.isEmpty(sfWarrantyRecord.getSn())) {
                    continue;
                }
                //将SN码转换成大写
                sfWarrantyRecord.setSn(sfWarrantyRecord.getSn().toUpperCase());
                if (StringUtils.isEmpty(sfWarrantyRecord.getSfUserId())) {
                    saveSyncFailInfo(sfWarrantyRecord.getSn(), null, null);
                    continue;
                }
                Long userId = userQueryService.getUserIdBySfUserId(sfWarrantyRecord.getSfUserId());
                if (userId == null) {
                    saveSyncFailInfo(sfWarrantyRecord.getSn(), sfWarrantyRecord.getSfUserId(), "IoT userId is null");
                    continue;
                }

                LocalDateTime purchaseTime = null;
                if (StringUtils.isNotEmpty(sfWarrantyRecord.getPurchaseDate())) {
                    LocalDate date = LocalDate.parse(sfWarrantyRecord.getPurchaseDate(), dateTimeFormatter);
                    if (date.getYear() >= IotAppCommonConstant.SQL_YEAR_LIMIT || date.getYear() < MIN_PURCHASE_YEAR) {
                        saveSyncFailInfo(sfWarrantyRecord.getSn(), sfWarrantyRecord.getSfUserId(),
                                "purchase time is error, year:" + date.getYear());
                        continue;
                    }
                    purchaseTime = date.atTime(0, 0, 0);
                }
                boolean existsTemp = existsSyncTemp(sfWarrantyRecord.getSn(), sfWarrantyRecord.getSfUserId());
                String deviceId = null;
                if (!existsTemp) {
                    try {
                        deviceId = bindSnOfCrm(sfWarrantyRecord.getSn(), userId);
                    } catch (Exception e) {
                        log.warn("User ID is:{} and sn is:{} ,Exception: ", userId, sfWarrantyRecord.getSn(), e);
                    }
                }

                if (StringUtils.isEmpty(deviceId)) {
                    DeviceRpcVo deviceRpcVo = remoteDeviceManageService.deviceDetailBySn(sfWarrantyRecord.getSn());
                    if (deviceRpcVo == null || StringUtils.isEmpty(deviceRpcVo.getDeviceId())) {
                        saveSyncFailInfo(sfWarrantyRecord.getSn(), sfWarrantyRecord.getSfUserId(), "deviceId is null");
                        continue;
                    }
                    deviceId = deviceRpcVo.getDeviceId();
                }

                DeviceInfo deviceInfo = new DeviceInfo();
                deviceInfo.setLastSyncTime(LocalDateTime.now());
                deviceInfo.setPurchaseTime(purchaseTime);
                assembleDeviceInfoPurchasePlace(sfWarrantyRecord, deviceInfo);
                assembleDeviceInfoReceipt(sfWarrantyRecord, deviceInfo);
                assembleDeviceInfoApply(sfWarrantyRecord, deviceInfo);
                deviceInfo.setSfId(sfWarrantyRecord.getId());

                DeviceInfo deviceInfoInDb = this.getOne(new LambdaQueryWrapper<DeviceInfo>()
                        .select(DeviceInfo::getId, DeviceInfo::getSfId)
                        .eq(DeviceInfo::getDeviceId, deviceId).last("LIMIT 1"));
                //已经注册过质保
                if(deviceInfoInDb != null) {
                    if(StringUtils.isEmpty(deviceInfoInDb.getSfId()) || deviceInfoInDb.getSfId().equals(sfWarrantyRecord.getId())) {
                        this.update(deviceInfo, new LambdaQueryWrapper<DeviceInfo>().eq(DeviceInfo::getDeviceId, deviceId));
                    } else {
                        // 不更新，记录日志
                        log.info("warranty registered, deviceId:{}, warranty info:{}", deviceId, JSONObject.toJSONString(sfWarrantyRecord));
                    }
                } else {
                    deviceInfo.setDeviceId(deviceId);
                    deviceInfo.setSourceCode(CommonConstant.ONE);
                    deviceInfoList.add(deviceInfo);
                }

                if (!existsTemp) {
                    saveSyncTempInfo(sfWarrantyRecord);
                }
            } catch (Exception e) {
                log.warn("syncDeviceFromCrm, sn:{}, user:{}, error: ", sfWarrantyRecord.getSn(), sfWarrantyRecord.getSfUserId(), e);
                saveSyncFailInfo(sfWarrantyRecord.getSn(), sfWarrantyRecord.getSfUserId(), null);
            }
        }
        this.saveBatch(deviceInfoList);
    }

    /**
     * 判断是否为IoT设备
     *
     * @param sn 设备SN码
     */
    private boolean isIoTDevice(String sn) {
        String snCodeFromSn = getSnCodeFromSn(sn);
        ProductRpcVo productRpcVo = remoteProductService.getProductIdBySnCode(snCodeFromSn);
        if (productRpcVo != null) {
            return ProductTypeEnum.GATEWAY_DEVICE.getValue().equals(productRpcVo.getProductType()) ||
                    ProductTypeEnum.GATEWAY_SUB_DEVICE.getValue().equals(productRpcVo.getProductType()) ||
                    ProductTypeEnum.DIRECT_CONNECTED_DEVICE.getValue().equals(productRpcVo.getProductType());
        }
        return false;
    }

    /**
     * 绑定设备
     *
     * @param sn     设备SN码
     * @param userId 用户ID
     * @return deviceId
     */
    private String bindSnOfCrm(String sn, Long userId) {
        String deviceId = null;
        //根据SN码查询多码表
        DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetailBySn(sn);
        if (deviceCodeRpcVo != null) {
            deviceId = deviceCodeRpcVo.getDeviceId();
        }
        boolean isIotDevice = isIoTDevice(sn);

        //IoT且多码缺失，则按异常处理
        if (StringUtils.isEmpty(deviceId) && isIotDevice) {
            return null;
        }

        AppUserDevice appUserDevice = new AppUserDevice();
        DeviceBindBo deviceBindBo = null;
        if (isIotDevice) {
            //IoT设备根据deviceId绑定
            try {
                //绑定方法中，若对应的product不存在会报错，若设备已停用则返回3，若被其它APP绑定过则返回1
                deviceBindBo = remoteDeviceManageService.bindByDeviceId(deviceId, 1, userId, null);
            } catch (Exception e) {
                log.error("Exception when remote bind device(deviceId:{}) and user({}), ", deviceId, userId, e);
            }
        } else {
            //非IoT设备根据SN码绑定
            try {
                deviceBindBo = remoteDeviceManageService.bindBySn(sn, 1, userId, null);
            } catch (Exception e) {
                log.error("Exception when bind device(sn:{}) and user({}), ", sn, userId, e);
            }
        }

        if (deviceBindBo != null && deviceBindBo.getResult() == 0) {
            deviceId = deviceBindBo.getDeviceId();
            appUserDevice.setUserId(userId);
            appUserDevice.setDeviceId(deviceId);
            appUserDevice.setSourceCode(CommonConstant.ONE);
            Object savepoint = null;
            boolean transactionActive = TransactionSynchronizationManager.isActualTransactionActive();
            if(transactionActive) {
                savepoint = TransactionAspectSupport.currentTransactionStatus().createSavepoint();
            }
            try {
                appDeviceService.bindFromSf(appUserDevice, deviceBindBo, isIotDevice);
            } catch (Exception e) {
                log.error("Exception when bind device(deviceId:{}) and user({}), ", deviceId, userId, e);
                if(savepoint != null) {
                    TransactionAspectSupport.currentTransactionStatus().rollbackToSavepoint(savepoint);
                }
            }
        }
        return deviceId;
    }

    /**
     * 设置设备购买地点
     */
    private void assembleDeviceInfoPurchasePlace(SfWarrantyRecord sfWarrantyRecord, DeviceInfo deviceInfo) {
        if (StringUtils.isEmpty(sfWarrantyRecord.getPurchasePlace())) {
            deviceInfo.setPurchasePlace(IotAppCommonConstant.OTHER);
            return;
        }
        String purchasePlace0 = sfWarrantyRecord.getPurchasePlace();
        String purchasePlace = purchasePlace0.equals(PurchasePlaceEnum.ACE_HARDWARE.getLabel()) ?
                PurchasePlaceEnum.ACE_HARDWARE.getValue().toLowerCase() : purchasePlace0.toLowerCase();
        List<DictVo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(),
                Collections.singletonList(IotAppCommonConstant.PURCHASE_PLACE));
        Map<String, String> map = dictList.stream().flatMap(dictVo -> dictVo.getNodes().stream())
                .collect(Collectors.toMap(DictNode::getDescription, DictNode::getLabel));
        Map<String, String> map2 = map.entrySet().stream()
                .collect(Collectors.toMap(
                        // 将 key 转为小写
                        entry -> entry.getKey().toLowerCase().replace("'", ""),
                        Map.Entry::getValue
                ));
        if (map2.containsKey(purchasePlace)) {
            deviceInfo.setPurchasePlace(map2.get(purchasePlace));
            deviceInfo.setPurchasePlaceOther(sfWarrantyRecord.getPurchasePlaceOther());
            return;
        }

        deviceInfo.setPurchasePlace(IotAppCommonConstant.OTHER);
        deviceInfo.setPurchasePlaceOther(sfWarrantyRecord.getPurchasePlaceOther());
    }

    /**
     * 设置设备票据信息
     */
    private void assembleDeviceInfoReceipt(SfWarrantyRecord sfWarrantyRecord, DeviceInfo deviceInfo) {
        if (StringUtils.isNotEmpty(sfWarrantyRecord.getReceiptUrl())) {
            deviceInfo.setReceiptInformation(ReceiptInformationStatusEnum.HAVE_RECEIPT.getValue());
            return;
        }

        if (StringUtils.isEmpty(sfWarrantyRecord.getReceiptStatus())) {
            deviceInfo.setReceiptInformation(ReceiptInformationStatusEnum.LOST_RECEIPT.getValue());
            return;
        }
        String receiptStatus = sfWarrantyRecord.getReceiptStatus().toUpperCase();
        if (receiptStatus.contains(ReceiptInformationStatusEnum.GIFT.name())) {
            deviceInfo.setReceiptInformation(ReceiptInformationStatusEnum.GIFT.getValue());
            return;
        }
        deviceInfo.setReceiptInformation(ReceiptInformationStatusEnum.LOST_RECEIPT.getValue());
    }

    /**
     * 设置设备用途
     */
    private void assembleDeviceInfoApply(SfWarrantyRecord sfWarrantyRecord, DeviceInfo deviceInfo) {
        if (StringUtils.isEmpty(sfWarrantyRecord.getUseType()) ||
                sfWarrantyRecord.getUseType().toLowerCase().contains(ApplyWithEnum.RESIDENTIAL.name().toLowerCase())) {
            deviceInfo.setApplyWith(ApplyWithEnum.RESIDENTIAL.getValue());
        } else {
            deviceInfo.setApplyWith(ApplyWithEnum.INDUSTRIAL_PROFESSIONAL_COMMERCIAL.getValue());
        }
    }

    /**
     * 保存同步异常信息
     */
    private void saveSyncFailInfo(String sn, String sfUserId, String msg) {
        boolean exists = deviceInfoSyncFailMapper.exists(new LambdaQueryWrapper<DeviceInfoSyncFail>()
                .eq(DeviceInfoSyncFail::getSn, sn));
        if (!exists) {
            DeviceInfoSyncFail entity = new DeviceInfoSyncFail();
            entity.setSn(sn);
            if (sn.length() > SN_LEN_LIMIT) {
                entity.setSn(sn.substring(0, SN_LEN_LIMIT));
            }
            entity.setSfUserId(sfUserId);
            entity.setMsg(msg);
            try {
                deviceInfoSyncFailMapper.insert(entity);
            } catch (Exception e) {
                log.warn("saveSyncFailInfo error, sn:{}, sfUserId:{}, ", sn, sfUserId, e);
            }
        }
    }

    /**
     * 保存质保同步缓存(来自SF)
     */
    private void saveSyncTempInfo(SfWarrantyRecord sfWarrantyRecord) {
        DeviceInfoSyncTemp entity = new DeviceInfoSyncTemp();
        entity.setSn(sfWarrantyRecord.getSn());
        entity.setSfUserId(sfWarrantyRecord.getSfUserId());
        entity.setWarrantyId(sfWarrantyRecord.getId());
        entity.setLastModifiedDate(sfWarrantyRecord.getLastModifiedDate());
        try {
            deviceInfoSyncTempMapper.insert(entity);
        } catch (Exception e) {
            log.warn("saveSyncTempInfo error, sn:{}, sfUserId:{}, ", entity.getSn(), entity.getSfUserId(), e);
        }
    }

    /**
     * 保存质保同步缓存(来自APP)
     * @param sfSn         sn
     * @param kitSnList    kitSn
     */
    private void saveSyncTemp(String sfSn, List<String> kitSnList) {
        UserVo userInfo = userQueryService.getUserInfo(StpUtil.getLoginIdAsLong());
        if(userInfo == null || StringUtils.isEmpty(userInfo.getSfUserId())) {
            return;
        }

        Set<String> snSet = Sets.newHashSet();
        snSet.add(sfSn);
        if(!CollectionUtils.isEmpty(kitSnList)) {
            snSet.addAll(kitSnList);
        }
        for(String sn : snSet) {
            if(existsSyncTemp(sn, userInfo.getSfUserId())) {
                continue;
            }
            DeviceInfoSyncTemp entity = new DeviceInfoSyncTemp();
            entity.setSn(sn);
            entity.setSfUserId(userInfo.getSfUserId());
            entity.setWarrantyId("");//避免对北美的影响，该字段数据库设置为非空
            try {
                deviceInfoSyncTempMapper.insert(entity);
            } catch (Exception e) {
                log.warn("saveSyncTempInfo error, sn:{}, sfUserId:{}, ", entity.getSn(), entity.getSfUserId(), e);
            }
        }
    }

    /**
     * 检查质保同步缓存是否存在
     */
    private boolean existsSyncTemp(String sn, String sfUserId) {
        return deviceInfoSyncTempMapper.exists(new LambdaQueryWrapper<DeviceInfoSyncTemp>()
                .eq(DeviceInfoSyncTemp::getSn, sn)
                .eq(DeviceInfoSyncTemp::getSfUserId, sfUserId));
    }
}
