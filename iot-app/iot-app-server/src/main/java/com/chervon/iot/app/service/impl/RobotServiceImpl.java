package com.chervon.iot.app.service.impl;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.amazonaws.services.simpleemail.model.*;
import com.chervon.common.core.constant.StringPool;
import com.chervon.common.core.mail.MailUtils;
import com.chervon.common.core.mail.SendUsesMailBody;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.i18n.config.MessageConfig;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.domain.dto.RobotCheckEmailCodeDto;
import com.chervon.iot.app.domain.vo.RobotEmailCertificateVo;
import com.chervon.iot.app.service.RobotService;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.vo.DeviceRpcVo;
import com.chervon.usercenter.api.service.SaleForceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.chervon.iot.app.domain.cache.ConstString.ROBOT_FORGET_PASSWORD_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/7/27 19:55
 */
@Service
@Slf4j
public class RobotServiceImpl implements RobotService {

    private static final String FROM_DEFAULT = "<EMAIL>";
    private static final Pattern EMAIL_TITLE = Pattern.compile("(?<=<title>).*(?=</title>)");
    @Value("${mail.ses.region}")
    private String region;
    @Value("${mail.ses.accessId}")
    private String accessId;
    @Value("${mail.ses.secretKey}")
    private String secretKey;
    @Value("${mail.from}")
    private String sendFrom;
    @Value("${mail.template-name-robot}")
    private String mailTemplateName;
    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;
    @DubboReference(timeout = 20000)
    private SaleForceService saleForceService;
    @Autowired
    private MessageConfig messageConfig;

    private String getFrom() {
        if (StringUtils.isBlank(sendFrom) || !sendFrom.contains("@")) {
            return FROM_DEFAULT;
        }
        return sendFrom;
    }

    private AmazonSimpleEmailService getSendEmailClient() {
        AWSCredentials credentials = new BasicAWSCredentials(accessId, secretKey);
        return AmazonSimpleEmailServiceClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withRegion(region)
                .build();
    }

    @Override
    public RobotEmailCertificateVo getEmailByDeviceId(String deviceId) {
        RobotEmailCertificateVo res = new RobotEmailCertificateVo();
        if (StringUtils.isBlank(deviceId)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ID_BLANK);
        }
//        // 目前sf未维护产品信息，展示无法注册维保，先用app注册邮箱返回
//        UserVo user = RedisUtils.getCacheObject(StpUtil.getTokenValue());
//        List<String> email = user == null ? null : new ArrayList<>(Collections.singletonList(user.getEmail()));
        // 根据deviceId获取设备sn
        DeviceRpcVo detail = remoteDeviceManageService.deviceDetail(deviceId);
        if (detail == null || StringUtils.isBlank(detail.getSn())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_SN_NOT_FOUND, deviceId);
        }
        String sn = detail.getSn();
        // 根据sn查询维保邮箱
        List<String> email = saleForceService.getWarrantyEmailBySn(sn);
        if (CollectionUtils.isEmpty(email)) {
            return res;
        }
        // 发送邮件，获取凭证
        String certificate = this.sendEmailCode(email);
        res.setEmails(email);
        res.setCertificate(certificate);
        return res;
    }

    public String sendEmailCode(List<String> email) {
        AmazonSimpleEmailService sendEmailClient = getSendEmailClient();
        if (CollectionUtils.isEmpty(email)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ROBOT_EMAIL_IS_EMPTY);
        }
        email.removeIf(s -> s == null || s.trim().isEmpty());
        if (email.isEmpty()) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ROBOT_EMAIL_IS_EMPTY);
        }
        String code = (int) ((Math.random() * 9 + 1) * 100000) + "";
        final String emailContent = getEmailContent(LocaleContextHolder.getLocale().getLanguage(), code);
        // 从模板中获取邮件主题
        Matcher matcher = EMAIL_TITLE.matcher(emailContent);
        String subject = "Robot reset password";
        if (matcher.find()) {
            subject = matcher.group(0);
        }
        try {
            // 构建发送邮件请求
            SendEmailRequest request = new SendEmailRequest()
                    .withDestination(new Destination().withToAddresses(email))
                    .withMessage(new Message().withBody(new Body().withHtml(new Content()
                                    .withCharset("UTF-8").withData(emailContent)))
                            .withSubject(new Content()
                                    .withCharset("UTF-8").withData(subject)))
                    .withSource(getFrom());
            // 发送邮件
            sendEmailClient.sendEmail(request);
            // 生成一个邮箱验证码verificationCode
            String verificationCode = SnowFlake.nextId() + "";
            String key = ROBOT_FORGET_PASSWORD_PREFIX + verificationCode;

            // 验证码有效期3小时
            RedisUtils.setCacheObject(key, code, Duration.ofHours(3));
            return verificationCode;
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ROBOT_SEND_EMAIL_CODE_ERROR);
        }
    }

    /**
     * 描述：获取邮件模板文件路径
     * @param templateName 邮件模板名称
     * @param language 语种
     */
    public String getMailFileName(String templateName, String language) {
        String[] split = templateName.split("\\.");
        return split[0] + StringPool.UNDERSCORE + language + StringPool.DOT + split[1];
    }

    private String getEmailContent(String lang, String code) {
        try {
            String fileName = getMailFileName(mailTemplateName, lang);
            HashMap<String, Object> map = new HashMap<>();
            map.put("code", code);
            String path = System.getProperty("user.dir") + File.separator + messageConfig.getBaseFolder() + File.separator;
            File file = new File(path + fileName);
            if (!file.exists()) {
                fileName = getMailFileName(mailTemplateName, "en");
            }
            return MailUtils.getTemplateMailText(path, fileName, map);
        } catch (Exception exception) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ROBOT_SEND_EMAIL_CODE_ERROR);
        }
    }

    @Override
    public void checkEmailCode(RobotCheckEmailCodeDto req) {
        if (req == null || req.getCertificate() == null) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ROBOT_EMAIL_CODE_ERROR);
        }
        String key = ROBOT_FORGET_PASSWORD_PREFIX + req.getCertificate();
        String code = RedisUtils.getCacheObject(key);
        if (StringUtils.isBlank(code)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ROBOT_EMAIL_CODE_EXPIRED);
        }
        if (!StringUtils.equals(req.getCode(), code)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ROBOT_EMAIL_CODE_NOT_MATCH);
        }
    }
}
