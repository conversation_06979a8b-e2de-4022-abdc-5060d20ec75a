package com.chervon.iot.app.domain.enums;

import com.chervon.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023-04-06 17:56
 **/
@AllArgsConstructor
@Getter
@Slf4j
public enum PurchasePlaceEnum {
    /**
     * Lowe's
     * ACE
     * Amazon LLC
     * Home Depot
     * Canadian Tire
     * Other
     */

    LOWE_S("Lowes", "Lowe's"),
    ACE_HARDWARE("ACE", "Ace Hardware"),
    AMAZON_LLC("Amazon LLC", "Amazon LLC"),
    HOME_DEPOT("Home Depot", "Home Depot"),
    CANADIAN_TIRE("Canadian Tire", "Canadian Tire"),
    OTHER("Other", "Other")
    ;

    private final String label;
    private final String value;


    public static String getLabelByValue(String value) {
        if (StringUtils.isEmpty(value)) {
            log.error("PurchasePlaceEnum#getLabelByValue -> value is empty.");
            return null;
        }
        for (PurchasePlaceEnum purchasePlaceEnum : PurchasePlaceEnum.values()) {
            if (purchasePlaceEnum.getValue().equals(value)) {
                return purchasePlaceEnum.getLabel();
            }
        }
        log.error("PurchasePlaceEnum#getLabelByValue -> no label match.");
        return null;
    }

}
