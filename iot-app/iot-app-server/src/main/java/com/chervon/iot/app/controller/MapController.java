package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.iot.app.domain.dto.AreaOperationDto;
import com.chervon.iot.app.domain.dto.DeviceMapDto;
import com.chervon.technology.api.RemoteMapService;
import com.chervon.technology.api.vo.map.DeviceAreaVo;
import com.chervon.technology.api.vo.map.DeviceMapVo;
import com.chervon.technology.api.vo.map.DevicePathNewVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-13
 */
@Api(tags = "R项目地图相关接口")
@RestController
@RequestMapping("/map")
@AllArgsConstructor
public class MapController {

    @DubboReference
    private RemoteMapService remoteMapService;

    @ApiOperation("根据deviceId获取地图")
    @PostMapping("getMapFileUrl")
    public DeviceMapVo lastInfo(@RequestBody DeviceMapDto req) {
        return remoteMapService.loadMapByDeviceId(req.getDeviceId());
    }

    @ApiOperation("根据deviceId获取上报路径")
    @PostMapping("getPathInfo")
    public List<DevicePathNewVo> getPathInfo(@RequestBody DeviceMapDto req) {
        return remoteMapService.loadPathByDeviceId(req.getDeviceId(), req.getPathIds());
    }

    @ApiOperation("新建、编辑区域名称")
    @PostMapping("area/store")
    public void areaStore(@RequestBody AreaOperationDto req) {
        remoteMapService.areaOperation(req.getDeviceId(), req.getAreaId(), req.getName(), req.getLevel(), "store");
    }

    @ApiOperation("删除区域名称")
    @PostMapping("area/delete")
    public void areaDelete(@RequestBody AreaOperationDto req) {
        remoteMapService.areaOperation(req.getDeviceId(), req.getAreaId(), req.getName(), req.getLevel(), "delete");
    }

    @ApiOperation("查询区域名称列表")
    @PostMapping("area/list")
    public List<DeviceAreaVo> areaList(@RequestBody SingleInfoReq<String> req) {
        return remoteMapService.areaList(req.getReq());
    }


}
