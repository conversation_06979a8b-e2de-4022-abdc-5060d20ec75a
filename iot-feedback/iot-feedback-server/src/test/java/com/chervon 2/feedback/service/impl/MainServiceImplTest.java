package com.chervon.feedback.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.i18n.util.MessageTools;
import com.chervon.feedback.api.exception.FeedbackException;
import com.chervon.feedback.domain.bo.DictBo;
import com.chervon.feedback.domain.bo.DictNodeBo;
import com.chervon.feedback.entity.Feedback;
import com.chervon.feedback.entity.FeedbackReply;
import com.chervon.feedback.req.FeedbackCsReplyDto;
import com.chervon.feedback.req.FeedbackExportDto;
import com.chervon.feedback.req.FeedbackPageDto;
import com.chervon.feedback.req.FeedbackSaveCsRemarkDto;
import com.chervon.feedback.resp.FeedbackDetailVo;
import com.chervon.feedback.resp.FeedbackExcel;
import com.chervon.feedback.resp.FeedbackPageVo;
import com.chervon.feedback.service.DictService;
import com.chervon.feedback.service.FeedbackReplyService;
import com.chervon.feedback.service.FeedbackService;
import com.chervon.iot.app.api.RemoteUserSettingService;
import com.chervon.iot.app.api.vo.UserSettingBo;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.MessageDto;
import com.chervon.usercenter.api.service.RemoteAppUserService;
import com.chervon.usercenter.api.vo.AppUserVo;
import com.chervon.usercenter.api.vo.UserVo;
import cn.hutool.extra.spring.SpringUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MainServiceImpl单元测试类
 *
 * <AUTHOR>
 * @date 2023/12/01
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MainServiceImpl单元测试")
class MainServiceImplTest {

    @Mock
    private DictService dictService;

    @Mock
    private FeedbackService feedbackService;

    @Mock
    private FeedbackReplyService feedbackReplyService;

    @Mock
    private AwsProperties awsProperties;

    @Mock
    private RemoteAppUserService remoteAppUserService;

    @Mock
    private RemoteUserSettingService remoteUserSettingService;

    @Mock
    private RemoteMessageService remoteMessageService;

    @Mock
    private MessageTools messageTools;

    private MainServiceImpl mainService;

    private FeedbackPageDto feedbackPageDto;
    private Feedback feedback;
    private FeedbackReply feedbackReply;
    private AppUserVo appUserVo;
    private UserVo userVo;
    private UserSettingBo userSettingBo;

    @BeforeEach
    void setUp() {
        // 手动创建 MainServiceImpl 实例并注入依赖
        mainService = new MainServiceImpl(feedbackService, feedbackReplyService, awsProperties);
        // 使用反射注入其他 mock 对象
        try {
            java.lang.reflect.Field dictServiceField = MainServiceImpl.class.getDeclaredField("dictService");
            dictServiceField.setAccessible(true);
            dictServiceField.set(mainService, dictService);

            java.lang.reflect.Field remoteAppUserServiceField = MainServiceImpl.class.getDeclaredField("remoteAppUserService");
            remoteAppUserServiceField.setAccessible(true);
            remoteAppUserServiceField.set(mainService, remoteAppUserService);

            java.lang.reflect.Field remoteUserSettingServiceField = MainServiceImpl.class.getDeclaredField("remoteUserSettingService");
            remoteUserSettingServiceField.setAccessible(true);
            remoteUserSettingServiceField.set(mainService, remoteUserSettingService);

            java.lang.reflect.Field remoteMessageServiceField = MainServiceImpl.class.getDeclaredField("remoteMessageService");
            remoteMessageServiceField.setAccessible(true);
            remoteMessageServiceField.set(mainService, remoteMessageService);

            java.lang.reflect.Field messageToolsField = MainServiceImpl.class.getDeclaredField("messageTools");
            messageToolsField.setAccessible(true);
            messageToolsField.set(mainService, messageTools);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 初始化测试数据
        setupTestData();
    }

    private void setupTestData() {
        // 初始化FeedbackPageDto
        feedbackPageDto = new FeedbackPageDto();
        feedbackPageDto.setPageNum(1);
        feedbackPageDto.setPageSize(10);
        feedbackPageDto.setUserId("123");
        // 不设置email，这样会调用convertToVoListWithEmails方法，进而调用listUserEmailByUserIds
        feedbackPageDto.setFeedbackId("1");
        feedbackPageDto.setFeedbackCategory("device");
        feedbackPageDto.setReplyState("pending");

        // 初始化Feedback
        feedback = new Feedback();
        feedback.setId(1L);
        feedback.setUserId(123L);
        feedback.setContent("测试反馈内容");
        feedback.setCategory1("device");
        feedback.setCategory2("robot");
        feedback.setReplyState("pending");
        feedback.setCreateTime(LocalDateTime.now());
        feedback.setDeviceSn("SN123456");

        // 初始化FeedbackReply
        feedbackReply = new FeedbackReply();
        feedbackReply.setId(1L);
        feedbackReply.setFeedbackId(1L);
        feedbackReply.setType("customer_service");
        feedbackReply.setContent("客服回复内容");
        feedbackReply.setCreateTime(LocalDateTime.now());

        // 初始化AppUserVo
        appUserVo = new AppUserVo();
        appUserVo.setUserId("123");
        appUserVo.setEmail("<EMAIL>");

        // 初始化UserVo
        userVo = new UserVo();
        userVo.setId(123L);
        userVo.setEmail("<EMAIL>");

        // 初始化UserSettingBo
        userSettingBo = new UserSettingBo();
        userSettingBo.setUserId(123L);
        userSettingBo.setPushToken("test_token");
        userSettingBo.setSystemMessageSwitch(1);
        userSettingBo.setLanguage("zh");
    }

    // 辅助方法：创建模拟的AWS配置
    private Object createMockPictureBucket() {
        return mock(Object.class);
    }

    // 辅助方法：创建模拟的字典数据
    private List<DictBo> createMockDictList() {
        List<DictBo> dictList = new ArrayList<>();

        // 创建手机类型字典（使用正确的常量名称）
        DictBo phoneTypeDict = new DictBo();
        phoneTypeDict.setDictName("appType"); // 使用FeedbackConstant.COMMIT_PHONE_TYPE的值
        List<DictNodeBo> phoneTypeNodes = new ArrayList<>();
        DictNodeBo androidNode = new DictNodeBo();
        androidNode.setLabel("android");
        androidNode.setDescription("安卓");
        phoneTypeNodes.add(androidNode);
        phoneTypeDict.setNodes(phoneTypeNodes);
        dictList.add(phoneTypeDict);

        // 创建反馈分类字典
        DictBo categoryDict = new DictBo();
        categoryDict.setDictName("feedbackCategory");
        List<DictNodeBo> categoryNodes = new ArrayList<>();
        DictNodeBo deviceNode = new DictNodeBo();
        deviceNode.setLabel("device");
        deviceNode.setDescription("设备问题");
        categoryNodes.add(deviceNode);
        categoryDict.setNodes(categoryNodes);
        dictList.add(categoryDict);

        // 创建更多分类字典
        DictBo moreCategoryDict = new DictBo();
        moreCategoryDict.setDictName("feedbackMoreCategory");
        List<DictNodeBo> moreCategoryNodes = new ArrayList<>();
        DictNodeBo appNode = new DictNodeBo();
        appNode.setLabel("app");
        appNode.setDescription("应用问题");
        moreCategoryNodes.add(appNode);
        moreCategoryDict.setNodes(moreCategoryNodes);
        dictList.add(moreCategoryDict);

        // 创建回复状态字典
        DictBo replyStateDict = new DictBo();
        replyStateDict.setDictName("feedbackReplyState");
        List<DictNodeBo> replyStateNodes = new ArrayList<>();
        DictNodeBo pendingNode = new DictNodeBo();
        pendingNode.setLabel("pending");
        pendingNode.setDescription("待回复");
        replyStateNodes.add(pendingNode);
        replyStateDict.setNodes(replyStateNodes);
        dictList.add(replyStateDict);

        // 创建回复类型字典
        DictBo replyTypeDict = new DictBo();
        replyTypeDict.setDictName("feedbackReplayType");
        List<DictNodeBo> replyTypeNodes = new ArrayList<>();
        DictNodeBo csNode = new DictNodeBo();
        csNode.setLabel("customer_service");
        csNode.setDescription("客服回复");
        replyTypeNodes.add(csNode);
        replyTypeDict.setNodes(replyTypeNodes);
        dictList.add(replyTypeDict);

        return dictList;
    }

    @Test
    @DisplayName("测试分页查询反馈信息 - 正常情况")
    void testPageWithValidRequest() {
        // Given
        Page<Feedback> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Collections.singletonList(feedback));
        mockPage.setTotal(1);

        when(feedbackService.page(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);
        // 模拟 remoteAppUserService.listUserEmailByUserIds 返回用户信息
        when(remoteAppUserService.listUserEmailByUserIds(anyList())).thenReturn(Collections.singletonList(userVo));

        // When
        PageResult<FeedbackPageVo> result = mainService.page(feedbackPageDto);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(feedback.getId(), result.getList().get(0).getFeedbackId());
        assertEquals(feedback.getContent(), result.getList().get(0).getFeedbackContent());

        verify(feedbackService).page(any(Page.class), any(LambdaQueryWrapper.class));
        verify(remoteAppUserService).listUserEmailByUserIds(anyList());
    }

    @Test
    @DisplayName("测试分页查询反馈信息 - 空结果")
    void testPageWithEmptyResult() {
        // Given
        Page<Feedback> mockPage = new Page<>(1, 10);
        mockPage.setRecords(Collections.emptyList());
        mockPage.setTotal(0);

        when(feedbackService.page(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

        // When
        PageResult<FeedbackPageVo> result = mainService.page(feedbackPageDto);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.getList().isEmpty());

        verify(feedbackService).page(any(Page.class), any(LambdaQueryWrapper.class));
        verify(remoteAppUserService, never()).listUserEmailByUserIds(anyList());
    }

    @Test
    @DisplayName("测试获取反馈详情 - 正常情况")
    void testDetailWithValidFeedbackId() {
        // Given
        Long feedbackId = 1L;
        when(feedbackService.getById(feedbackId)).thenReturn(feedback);
        when(remoteAppUserService.getAppUser(feedback.getUserId())).thenReturn(appUserVo);
        when(feedbackReplyService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.singletonList(feedbackReply));

        // When
        FeedbackDetailVo result = mainService.detail(feedbackId);

        // Then
        assertNotNull(result);
        assertEquals(feedbackId, result.getFeedbackId());
        assertEquals(feedback.getContent(), result.getFeedbackContent());
        assertEquals(feedback.getCategory1(), result.getFeedbackCategory());
        assertEquals(appUserVo.getEmail(), result.getEmail());
        assertEquals(1, result.getReplies().size());

        verify(feedbackService).getById(feedbackId);
        verify(remoteAppUserService).getAppUser(feedback.getUserId());
        verify(feedbackReplyService).list(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("测试获取反馈详情 - feedbackId为null")
    void testDetailWithNullFeedbackId() {
        // When & Then
        assertThrows(FeedbackException.class, () -> mainService.detail(null));

        verify(feedbackService, never()).getById(any());
    }

    @Test
    @DisplayName("测试保存客服备注 - 正常情况")
    void testSaveCsRemarkWithValidRequest() {
        // Given
        FeedbackSaveCsRemarkDto request = new FeedbackSaveCsRemarkDto();
        request.setFeedbackId(1L);
        request.setCsRemark("客服备注内容");

        when(feedbackService.getById(request.getFeedbackId())).thenReturn(feedback);
        when(feedbackService.updateById(any(Feedback.class))).thenReturn(true);

        // When
        assertDoesNotThrow(() -> mainService.saveCsRemark(request));

        // Then
        ArgumentCaptor<Feedback> feedbackCaptor = ArgumentCaptor.forClass(Feedback.class);
        verify(feedbackService).updateById(feedbackCaptor.capture());

        Feedback updatedFeedback = feedbackCaptor.getValue();
        assertEquals(request.getFeedbackId(), updatedFeedback.getId());
        assertEquals(request.getCsRemark(), updatedFeedback.getCsRemark());
    }

    @Test
    @DisplayName("测试保存客服备注 - feedbackId为null")
    void testSaveCsRemarkWithNullFeedbackId() {
        // Given
        FeedbackSaveCsRemarkDto request = new FeedbackSaveCsRemarkDto();
        request.setFeedbackId(null);
        request.setCsRemark("客服备注内容");

        // When & Then
        assertThrows(FeedbackException.class, () -> mainService.saveCsRemark(request));

        verify(feedbackService, never()).getById(any());
        verify(feedbackService, never()).updateById(any());
    }

    @Test
    @DisplayName("测试客服回复 - 正常情况")
    void testCsReplyWithValidRequest() {
        // Given
        FeedbackCsReplyDto request = new FeedbackCsReplyDto();
        request.setFeedbackId(1L);
        request.setContent("客服回复内容");
        request.setPictures(Arrays.asList("pic1.jpg", "pic2.jpg"));

        feedback.setReplyState("pending");

        when(feedbackService.getById(request.getFeedbackId())).thenReturn(feedback);
        when(feedbackReplyService.save(any(FeedbackReply.class))).thenReturn(true);
        when(feedbackService.updateById(any(Feedback.class))).thenReturn(true);

        // 模拟AWS配置
        AwsProperties.BucketProperties bucketProperties = new AwsProperties.BucketProperties();
        bucketProperties.setCdnHost("https://test-cdn.example.com");
        when(awsProperties.getPictureBucket()).thenReturn(bucketProperties);

        when(remoteAppUserService.getUserAppTypeCodeByUserId(anyLong())).thenReturn("ANDROID");
        when(remoteUserSettingService.get(anyLong())).thenReturn(userSettingBo);
        when(messageTools.getCodeValue(anyString(), anyString())).thenReturn("反馈消息标题");

        // When
        assertDoesNotThrow(() -> mainService.csReply(request));

        // Then
        verify(feedbackService).getById(request.getFeedbackId());
        verify(feedbackReplyService).save(any(FeedbackReply.class));
        verify(feedbackService).updateById(any(Feedback.class));
        verify(remoteMessageService).pushMessage(anyList());
    }

    @Test
    @DisplayName("测试客服回复 - feedbackId为null")
    void testCsReplyWithNullFeedbackId() {
        // Given
        FeedbackCsReplyDto request = new FeedbackCsReplyDto();
        request.setFeedbackId(null);
        request.setContent("客服回复内容");

        // 使用MockedStatic模拟SpringUtil的静态方法
        try (MockedStatic<SpringUtil> springUtilMocked = mockStatic(SpringUtil.class)) {
            // 模拟SpringUtil.getBean("messageTools")返回模拟的MessageTools
            springUtilMocked.when(() -> SpringUtil.getBean("messageTools")).thenReturn(messageTools);

            // When & Then
            assertThrows(FeedbackException.class, () -> mainService.csReply(request));

            verify(feedbackService, never()).getById(any());
        }
    }

    @Test
    @DisplayName("测试推送反馈消息 - 正常情况")
    void testPushFeedbackMsgWithValidFeedback() {
        // Given
        feedback.setReplyContent("客服回复内容");

        when(remoteAppUserService.getUserAppTypeCodeByUserId(anyLong())).thenReturn("ANDROID");
        when(remoteUserSettingService.get(anyLong())).thenReturn(userSettingBo);
        when(messageTools.getCodeValue(anyString(), anyString())).thenReturn("反馈消息标题");

        // When
        assertDoesNotThrow(() -> mainService.pushFeedbackMsg(feedback));

        // Then
        ArgumentCaptor<List<MessageDto>> messageCaptor = ArgumentCaptor.forClass(List.class);
        verify(remoteMessageService).pushMessage(messageCaptor.capture());

        List<MessageDto> messages = messageCaptor.getValue();
        assertNotNull(messages);
        assertEquals(1, messages.size());

        MessageDto messageDto = messages.get(0);
        assertEquals(String.valueOf(feedback.getUserId()), messageDto.getUserId());
        assertEquals(String.valueOf(feedback.getId()), messageDto.getSystemMessageId());
        assertEquals(feedback.getReplyContent(), messageDto.getContent());
    }

    @Test
    @DisplayName("测试关闭反馈 - 正常情况")
    void testCloseFeedbackWithValidId() {
        // Given
        Long feedbackId = 1L;
        when(feedbackService.getById(feedbackId)).thenReturn(feedback);
        when(feedbackService.updateById(any(Feedback.class))).thenReturn(true);

        // When
        assertDoesNotThrow(() -> mainService.closeFeedback(feedbackId));

        // Then
        ArgumentCaptor<Feedback> feedbackCaptor = ArgumentCaptor.forClass(Feedback.class);
        verify(feedbackService).updateById(feedbackCaptor.capture());

        Feedback updatedFeedback = feedbackCaptor.getValue();
        assertEquals("closed", updatedFeedback.getReplyState());
    }

    @Test
    @DisplayName("测试导出反馈数据 - 正常情况")
    void testExportWithValidRequest() {
        // Given
        FeedbackExportDto request = new FeedbackExportDto();
        request.setUserId("123");
        request.setZone(8);

        List<Feedback> feedbackList = Collections.singletonList(feedback);
        List<DictBo> dictList = createMockDictList();

        when(feedbackService.list(any(LambdaQueryWrapper.class))).thenReturn(feedbackList);
        when(remoteAppUserService.listUserEmailByUserIds(anyList())).thenReturn(Collections.singletonList(userVo));
        when(feedbackReplyService.list(any())).thenReturn(Collections.emptyList());
        when(dictService.listByDictName(anyString(), anyList())).thenReturn(dictList);

        // When
        List<FeedbackExcel> result = mainService.export(request);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        FeedbackExcel excel = result.get(0);
        assertEquals(feedback.getId(), excel.getFeedbackId());
        assertEquals(userVo.getEmail(), excel.getEmail());

        verify(feedbackService).list(any(LambdaQueryWrapper.class));
        verify(dictService).listByDictName(anyString(), anyList());
    }

    @Test
    @DisplayName("测试导出反馈数据 - 空结果")
    void testExportWithEmptyResult() {
        // Given
        FeedbackExportDto request = new FeedbackExportDto();
        request.setUserId("123");

        when(feedbackService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // When
        List<FeedbackExcel> result = mainService.export(request);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(feedbackService).list(any(LambdaQueryWrapper.class));
        verify(dictService, never()).listByDictName(anyString(), anyList());
    }
}