<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="message-center-server" />
        <module name="chervon-common-mybatis" />
        <module name="chervon-common-i18n" />
        <module name="chervon-common-security" />
        <module name="chervon-common-core" />
        <module name="chervon-common-excel" />
        <module name="authority-platform-server" />
        <module name="chervon-common-elasticsearch" />
        <module name="chervon-common-mongodb" />
        <module name="chervon-common-idempotent" />
        <module name="chervon-common-swagger" />
        <module name="configuration-center-sdk-language" />
        <module name="message-center-api" />
        <module name="authority-platform-api" />
        <module name="configuration-center-server" />
        <module name="iot-middle-platform-api" />
        <module name="iot-platform-server" />
        <module name="iot-middle-platform-server" />
        <module name="chervon-common-log" />
        <module name="chervon-common-redis" />
        <module name="iot-platform-api" />
        <module name="chervon-common-dubbo" />
        <module name="configuration-center-api" />
        <module name="chervon-common-job" />
        <module name="iot-app-server" />
        <module name="chervon-common-seata" />
        <module name="chervon-common-idgenerator" />
        <module name="chervon-common-web" />
        <module name="iot-app-api" />
        <module name="chervon-common-mail" />
        <module name="operation-platform-api" />
        <module name="operation-platform-server" />
        <module name="authority-platform-sdk" />
        <module name="chervon-common-oss" />
        <module name="chervon-common-satoken" />
        <module name="chervon-common-sso" />
        <module name="user-center-api" />
        <module name="user-center-server" />
        <module name="iot-feedback-api" />
        <module name="iot-feedback-server" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="8" />
  </component>
</project>