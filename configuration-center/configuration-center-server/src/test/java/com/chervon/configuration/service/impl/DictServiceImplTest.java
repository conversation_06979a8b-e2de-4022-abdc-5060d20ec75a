package com.chervon.configuration.service.impl;

import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.i18n.util.MessageTools;
import com.chervon.configuration.resp.DictBo;
import com.chervon.configuration.resp.DictNodeBo;
import com.google.gson.JsonSyntaxException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * DictServiceImpl单元测试类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DictServiceImpl单元测试")
class DictServiceImplTest {

    @Mock
    private MessageTools messageTools;

    @InjectMocks
    private DictServiceImpl dictService;

    private static final String TEST_LANG = "zh";
    private static final String TEST_DICT_NAME = "testDict";
    private static final String VALID_JSON = "[{\"label\":\"test1\",\"description\":\"测试1\"},{\"label\":\"test2\",\"description\":\"测试2\"}]";
    private static final String INVALID_JSON = "{invalid json format";
    private static final String EMPTY_JSON = "[]";

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    @DisplayName("正常情况：返回有效字典数据")
    void testListByDictName_Success() {
        // Given
        List<String> dictNames = Arrays.asList(TEST_DICT_NAME);
        when(messageTools.getCodeValue(TEST_DICT_NAME, TEST_LANG)).thenReturn(VALID_JSON);

        // When
        List<DictBo> result = dictService.listByDictName(TEST_LANG, dictNames);

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个字典对象");
        
        DictBo dictBo = result.get(0);
        assertEquals(TEST_DICT_NAME, dictBo.getDictName(), "字典名称应匹配");
        assertEquals(2, dictBo.getNodes().size(), "应包含2个字典节点");
        
        DictNodeBo firstNode = dictBo.getNodes().get(0);
        assertEquals("test1", firstNode.getLabel(), "第一个节点label应匹配");
        assertEquals("测试1", firstNode.getDescription(), "第一个节点描述应匹配");
        
        verify(messageTools, times(1)).getCodeValue(TEST_DICT_NAME, TEST_LANG);
    }

    @Test
    @DisplayName("边界值测试：空字典名称列表")
    void testListByDictName_EmptyDictNames() {
        // Given
        List<String> emptyDictNames = Collections.emptyList();

        // When
        List<DictBo> result = dictService.listByDictName(TEST_LANG, emptyDictNames);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "空输入应返回空列表");
        
        verify(messageTools, never()).getCodeValue(anyString(), anyString());
    }

    @Test
    @DisplayName("边界值测试：MessageTools返回null值")
    void testListByDictName_MessageToolsReturnsNull() {
        // Given
        List<String> dictNames = Arrays.asList(TEST_DICT_NAME);
        when(messageTools.getCodeValue(TEST_DICT_NAME, TEST_LANG)).thenReturn(null);

        // When
        List<DictBo> result = dictService.listByDictName(TEST_LANG, dictNames);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "null值应导致空结果");
        
        verify(messageTools, times(1)).getCodeValue(TEST_DICT_NAME, TEST_LANG);
    }

    @Test
    @DisplayName("边界值测试：MessageTools返回空字符串")
    void testListByDictName_MessageToolsReturnsEmptyString() {
        // Given
        List<String> dictNames = Arrays.asList(TEST_DICT_NAME);
        when(messageTools.getCodeValue(TEST_DICT_NAME, TEST_LANG)).thenReturn("");

        // When
        List<DictBo> result = dictService.listByDictName(TEST_LANG, dictNames);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "空字符串应导致空结果");
        
        verify(messageTools, times(1)).getCodeValue(TEST_DICT_NAME, TEST_LANG);
    }

    @Test
    @DisplayName("正常情况：空JSON数组")
    void testListByDictName_EmptyJsonArray() {
        // Given
        List<String> dictNames = Arrays.asList(TEST_DICT_NAME);
        when(messageTools.getCodeValue(TEST_DICT_NAME, TEST_LANG)).thenReturn(EMPTY_JSON);

        // When
        List<DictBo> result = dictService.listByDictName(TEST_LANG, dictNames);

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个字典对象");
        
        DictBo dictBo = result.get(0);
        assertEquals(TEST_DICT_NAME, dictBo.getDictName(), "字典名称应匹配");
        assertTrue(dictBo.getNodes().isEmpty(), "节点列表应为空");
        
        verify(messageTools, times(1)).getCodeValue(TEST_DICT_NAME, TEST_LANG);
    }

    @Test
    @DisplayName("正常情况：多个字典名称")
    void testListByDictName_MultipleDictNames() {
        // Given
        String dictName1 = "dict1";
        String dictName2 = "dict2";
        List<String> dictNames = Arrays.asList(dictName1, dictName2);
        
        String json1 = "[{\"label\":\"label1\",\"description\":\"desc1\"}]";
        String json2 = "[{\"label\":\"label2\",\"description\":\"desc2\"}]";
        
        when(messageTools.getCodeValue(dictName1, TEST_LANG)).thenReturn(json1);
        when(messageTools.getCodeValue(dictName2, TEST_LANG)).thenReturn(json2);

        // When
        List<DictBo> result = dictService.listByDictName(TEST_LANG, dictNames);

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(2, result.size(), "应返回2个字典对象");
        
        assertEquals(dictName1, result.get(0).getDictName(), "第一个字典名称应匹配");
        assertEquals(dictName2, result.get(1).getDictName(), "第二个字典名称应匹配");
        
        verify(messageTools, times(1)).getCodeValue(dictName1, TEST_LANG);
        verify(messageTools, times(1)).getCodeValue(dictName2, TEST_LANG);
    }

    @Test
    @DisplayName("混合情况：部分字典有数据，部分无数据")
    void testListByDictName_MixedResults() {
        // Given
        String dictName1 = "validDict";
        String dictName2 = "emptyDict";
        List<String> dictNames = Arrays.asList(dictName1, dictName2);
        
        when(messageTools.getCodeValue(dictName1, TEST_LANG)).thenReturn(VALID_JSON);
        when(messageTools.getCodeValue(dictName2, TEST_LANG)).thenReturn("");

        // When
        List<DictBo> result = dictService.listByDictName(TEST_LANG, dictNames);

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "只应返回1个有效字典对象");
        assertEquals(dictName1, result.get(0).getDictName(), "返回的字典名称应匹配");
        
        verify(messageTools, times(1)).getCodeValue(dictName1, TEST_LANG);
        verify(messageTools, times(1)).getCodeValue(dictName2, TEST_LANG);
    }

    @Test
    @DisplayName("错误情况：MessageTools为null")
    void testListByDictName_MessageToolsIsNull() {
        // Given
        DictServiceImpl serviceWithNullMessageTools = new DictServiceImpl();
        List<String> dictNames = Arrays.asList(TEST_DICT_NAME);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            serviceWithNullMessageTools.listByDictName(TEST_LANG, dictNames);
        }, "MessageTools为null应抛出NullPointerException");
    }

    @Test
    @DisplayName("边界值测试：null语言参数")
    void testListByDictName_NullLang() {
        // Given
        List<String> dictNames = Arrays.asList(TEST_DICT_NAME);
        when(messageTools.getCodeValue(TEST_DICT_NAME, null)).thenReturn(VALID_JSON);

        // When
        List<DictBo> result = dictService.listByDictName(null, dictNames);

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应正常处理null语言参数");
        
        verify(messageTools, times(1)).getCodeValue(TEST_DICT_NAME, null);
    }

    @Test
    @DisplayName("性能测试：大量字典名称处理")
    void testListByDictName_LargeNumberOfDictNames() {
        // Given
        List<String> largeDictNames = Arrays.asList(
            "dict1", "dict2", "dict3", "dict4", "dict5",
            "dict6", "dict7", "dict8", "dict9", "dict10"
        );
        
        // Mock所有字典都返回有效JSON
        for (String dictName : largeDictNames) {
            when(messageTools.getCodeValue(dictName, TEST_LANG)).thenReturn(VALID_JSON);
        }

        // When
        long startTime = System.currentTimeMillis();
        List<DictBo> result = dictService.listByDictName(TEST_LANG, largeDictNames);
        long endTime = System.currentTimeMillis();

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(10, result.size(), "应返回10个字典对象");
        assertTrue(endTime - startTime < 1000, "处理时间应在合理范围内");
        
        // 验证所有字典都被调用
        for (String dictName : largeDictNames) {
            verify(messageTools, times(1)).getCodeValue(dictName, TEST_LANG);
        }
    }
}