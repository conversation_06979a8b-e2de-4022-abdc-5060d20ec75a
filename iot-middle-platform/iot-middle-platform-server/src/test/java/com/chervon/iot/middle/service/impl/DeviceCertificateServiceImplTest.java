package com.chervon.iot.middle.service.impl;

import com.amazonaws.services.iot.AWSIot;
import com.amazonaws.services.iot.model.AttachPolicyRequest;
import com.amazonaws.services.iot.model.CreateKeysAndCertificateRequest;
import com.amazonaws.services.iot.model.CreateKeysAndCertificateResult;
import com.amazonaws.services.iot.model.KeyPair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.iot.middle.domain.pojo.DeviceCertificate;
import com.chervon.iot.middle.mapper.DeviceCertificateMapper;
import com.chervon.iot.middle.service.AwsIotService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DeviceCertificateServiceImpl 单元测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-05-30
 * @description 设备证书服务实现类的单元测试，遵循BCDE原则进行全面测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("设备证书服务实现类测试")
class DeviceCertificateServiceImplTest {

    private static final String TEST_DEVICE_ID_1 = "device001";
    private static final String TEST_DEVICE_ID_2 = "device002";
    private static final String TEST_DEVICE_ID_3 = "device003";
    private static final String TEST_CERTIFICATE_ID = "cert123";
    private static final String TEST_CERTIFICATE_PEM = "-----BEGIN CERTIFICATE-----\ntest\n-----END CERTIFICATE-----";
    private static final String TEST_CERTIFICATE_ARN = "arn:aws:iot:us-east-1:123456789012:cert/test";
    private static final String TEST_PRIVATE_KEY = "-----BEGIN RSA PRIVATE KEY-----\ntest\n-----END RSA PRIVATE KEY-----";
    private static final String TEST_POLICY_NAME = "TestPolicy";
    private static final Long TEST_ID = 1L;

    @Mock
    private AwsIotService awsIotService;

    @Mock
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Mock
    private DeviceCertificateMapper deviceCertificateMapper;

    @Mock
    private AWSIot awsIot;

    @Mock
    private CreateKeysAndCertificateResult certResult;

    @Mock
    private KeyPair keyPair;

    @InjectMocks
    private DeviceCertificateServiceImpl deviceCertificateService;

    @BeforeEach
    void setUp() {
        // 设置 AwsIotService 的静态属性，使用lenient模式避免UnnecessaryStubbingException
        AwsProperties awsProperties = mock(AwsProperties.class);
        lenient().when(awsProperties.getPolicyName()).thenReturn(TEST_POLICY_NAME);
        AwsIotService.awsProperties = awsProperties;
    }

    // ==================== Border Tests (边界值测试) ====================

    @Test
    @DisplayName("边界测试 - 空列表输入应返回空列表")
    void testApplyForCertificate_EmptyList_ShouldReturnEmptyList() {
        // Given
        List<String> emptyList = new ArrayList<>();

        // When
        List<DeviceCertificate> result = deviceCertificateService.applyForCertificate(emptyList);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(awsIotService, threadPoolTaskExecutor);
    }

    @Test
    @DisplayName("边界测试 - null输入应返回空列表")
    void testApplyForCertificate_NullInput_ShouldReturnEmptyList() {
        // When
        List<DeviceCertificate> result = deviceCertificateService.applyForCertificate(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(awsIotService, threadPoolTaskExecutor);
    }

    @Test
    @DisplayName("边界测试 - 单个设备ID应正常处理")
    void testApplyForCertificate_SingleDeviceId_ShouldProcessCorrectly() {
        // Given
        List<String> singleDeviceList = Collections.singletonList(TEST_DEVICE_ID_1);
        mockEmptyDatabaseQuery();
        mockAwsIotServiceCalls();

        // When
        List<DeviceCertificate> result = deviceCertificateService.applyForCertificate(singleDeviceList);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(TEST_DEVICE_ID_1, result.get(0).getDeviceId());
        verify(awsIotService, times(1)).getAwsIot();
    }

    @Test
    @DisplayName("边界测试 - 异步申请证书空列表输入应直接返回")
    void testAsyncApplyForCertificate_EmptyList_ShouldReturnDirectly() {
        // Given
        List<String> emptyList = new ArrayList<>();

        // When
        deviceCertificateService.asyncApplyForCertificate(emptyList);

        // Then
        verifyNoInteractions(threadPoolTaskExecutor);
    }

    @Test
    @DisplayName("边界测试 - 异步申请证书null输入应直接返回")
    void testAsyncApplyForCertificate_NullInput_ShouldReturnDirectly() {
        // When
        deviceCertificateService.asyncApplyForCertificate(null);

        // Then
        verifyNoInteractions(threadPoolTaskExecutor);
    }

    // ==================== Correct Tests (正确输入测试) ====================

    @Test
    @DisplayName("正确输入测试 - 数据库中已存在所有证书应直接返回")
    void testApplyForCertificate_AllCertificatesExist_ShouldReturnFromDatabase() {
        // Given
        List<String> deviceIds = Arrays.asList(TEST_DEVICE_ID_1, TEST_DEVICE_ID_2);
        List<DeviceCertificate> existingCertificates = createExistingCertificates();
        
        mockDatabaseQueryWithResults(existingCertificates);

        // When
        List<DeviceCertificate> result = deviceCertificateService.applyForCertificate(deviceIds);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(TEST_DEVICE_ID_1, result.get(0).getDeviceId());
        assertEquals(TEST_DEVICE_ID_2, result.get(1).getDeviceId());
        verifyNoInteractions(awsIotService);
    }

    @Test
    @DisplayName("正确输入测试 - 数据库中不存在证书需要新建")
    void testApplyForCertificate_NoCertificatesExist_ShouldCreateNew() {
        // Given
        List<String> deviceIds = Arrays.asList(TEST_DEVICE_ID_1, TEST_DEVICE_ID_2);
        mockEmptyDatabaseQuery();
        mockAwsIotServiceCalls();

        // When
        List<DeviceCertificate> result = deviceCertificateService.applyForCertificate(deviceIds);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(awsIotService, times(2)).getAwsIot();
        verify(awsIot, times(2)).createKeysAndCertificate(any(CreateKeysAndCertificateRequest.class));
        verify(awsIot, times(2)).attachPolicy(any(AttachPolicyRequest.class));
    }

    @Test
    @DisplayName("正确输入测试 - 部分存在部分不存在的混合情况")
    void testApplyForCertificate_MixedScenario_ShouldHandleCorrectly() {
        // Given
        List<String> deviceIds = Arrays.asList(TEST_DEVICE_ID_1, TEST_DEVICE_ID_2, TEST_DEVICE_ID_3);
        List<DeviceCertificate> existingCertificates = Collections.singletonList(createDeviceCertificate(TEST_DEVICE_ID_1));
        
        mockDatabaseQueryWithResults(existingCertificates);
        mockAwsIotServiceCalls();

        // When
        List<DeviceCertificate> result = deviceCertificateService.applyForCertificate(deviceIds);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        // 验证第一个是从数据库返回的
        assertEquals(TEST_DEVICE_ID_1, result.get(0).getDeviceId());
        // 验证后两个是新创建的
        verify(awsIotService, times(2)).getAwsIot();
    }

    @Test
    @DisplayName("正确输入测试 - 异步申请证书应正确执行")
    void testAsyncApplyForCertificate_ValidInput_ShouldExecuteAsync() {
        // Given
        List<String> deviceIds = Arrays.asList(TEST_DEVICE_ID_1, TEST_DEVICE_ID_2);
        
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run(); // 直接执行任务以便测试
            return null;
        }).when(threadPoolTaskExecutor).execute(any(Runnable.class));

        mockEmptyDatabaseQueryForAsync();
        mockAwsIotServiceCalls();

        // When
        deviceCertificateService.asyncApplyForCertificate(deviceIds);

        // Then
        verify(threadPoolTaskExecutor, times(1)).execute(any(Runnable.class));
        // 注意：由于异步执行，awsIotService的调用发生在异步任务中，测试时可能还未执行完成
        // 这里我们主要验证异步任务被正确提交
    }

    // ==================== Design Tests (设计文档相关测试) ====================

    @Test
    @DisplayName("设计测试 - 验证AWS IoT证书创建请求参数")
    void testDeviceCertificateCreate_ShouldSetCorrectParameters() throws Exception {
        // Given
        mockAwsIotServiceCalls();

        // When
        Method method = DeviceCertificateServiceImpl.class.getDeclaredMethod("deviceCertificateCreate", String.class);
        method.setAccessible(true);
        DeviceCertificate result = (DeviceCertificate) method.invoke(deviceCertificateService, TEST_DEVICE_ID_1);

        // Then
        ArgumentCaptor<CreateKeysAndCertificateRequest> requestCaptor = ArgumentCaptor.forClass(CreateKeysAndCertificateRequest.class);
        verify(awsIot).createKeysAndCertificate(requestCaptor.capture());
        
        CreateKeysAndCertificateRequest capturedRequest = requestCaptor.getValue();
        assertTrue(capturedRequest.getSetAsActive());
        
        assertNotNull(result);
        assertEquals(TEST_DEVICE_ID_1, result.getDeviceId());
        assertEquals(TEST_CERTIFICATE_ID, result.getCertificateId());
        assertEquals(TEST_CERTIFICATE_PEM, result.getCertificatePem());
        assertEquals(TEST_CERTIFICATE_ARN, result.getCertificateArn());
        assertEquals(TEST_PRIVATE_KEY, result.getPrivateKey());
    }

    @Test
    @DisplayName("设计测试 - 验证策略附加请求参数")
    void testDeviceCertificateCreate_ShouldAttachPolicyCorrectly() throws Exception {
        // Given
        mockAwsIotServiceCalls();

        // When
        Method method = DeviceCertificateServiceImpl.class.getDeclaredMethod("deviceCertificateCreate", String.class);
        method.setAccessible(true);
        method.invoke(deviceCertificateService, TEST_DEVICE_ID_1);

        // Then
        ArgumentCaptor<AttachPolicyRequest> policyCaptor = ArgumentCaptor.forClass(AttachPolicyRequest.class);
        verify(awsIot).attachPolicy(policyCaptor.capture());
        
        AttachPolicyRequest capturedPolicyRequest = policyCaptor.getValue();
        assertEquals(TEST_POLICY_NAME, capturedPolicyRequest.getPolicyName());
        assertEquals(TEST_CERTIFICATE_ARN, capturedPolicyRequest.getTarget());
    }

    // ==================== Error Tests (异常情况测试) ====================

    @Test
    @DisplayName("异常测试 - AWS IoT服务调用失败应抛出异常")
    void testDeviceCertificateCreate_AwsIotServiceFailure_ShouldThrowException() throws Exception {
        // Given
        when(awsIotService.getAwsIot()).thenReturn(awsIot);
        when(awsIot.createKeysAndCertificate(any(CreateKeysAndCertificateRequest.class)))
            .thenThrow(new RuntimeException("AWS IoT service error"));

        // When & Then
        Method method = DeviceCertificateServiceImpl.class.getDeclaredMethod("deviceCertificateCreate", String.class);
        method.setAccessible(true);
        
        assertThrows(java.lang.reflect.InvocationTargetException.class, () -> {
            method.invoke(deviceCertificateService, TEST_DEVICE_ID_1);
        });
        
        // 验证InvocationTargetException的cause是期望的RuntimeException
        try {
            method.invoke(deviceCertificateService, TEST_DEVICE_ID_1);
            fail("Expected InvocationTargetException to be thrown");
        } catch (java.lang.reflect.InvocationTargetException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("AWS IoT service error", e.getCause().getMessage());
        }
    }

    @Test
    @DisplayName("异常测试 - 数据库操作异常应正确处理")
    void testApplyForCertificate_DatabaseException_ShouldThrowException() {
        // Given
        List<String> deviceIds = Collections.singletonList(TEST_DEVICE_ID_1);
        when(deviceCertificateService.list(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("Database connection error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            deviceCertificateService.applyForCertificate(deviceIds);
        });
    }

    @Test
    @DisplayName("异常测试 - 异步执行异常应被捕获并记录")
    void testAsyncApplyForCertificate_ExecutionException_ShouldBeCaught() {
        // Given
        List<String> deviceIds = Collections.singletonList(TEST_DEVICE_ID_1);
        
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run(); // 直接执行任务以便测试异常处理
            return null;
        }).when(threadPoolTaskExecutor).execute(any(Runnable.class));

        lenient().when(deviceCertificateService.list(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("Async execution error"));

        // When & Then - 异常应被捕获，不会向上抛出
        assertDoesNotThrow(() -> {
            deviceCertificateService.asyncApplyForCertificate(deviceIds);
        });
        
        verify(threadPoolTaskExecutor, times(1)).execute(any(Runnable.class));
    }

    @Test
    @DisplayName("异常测试 - 线程池执行异常应正确处理")
    void testAsyncApplyForCertificate_ThreadPoolException_ShouldHandleGracefully() {
        // Given
        List<String> deviceIds = Collections.singletonList(TEST_DEVICE_ID_1);
        doThrow(new RuntimeException("Thread pool error")).when(threadPoolTaskExecutor).execute(any(Runnable.class));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            deviceCertificateService.asyncApplyForCertificate(deviceIds);
        });
    }

    // ==================== Private Helper Methods ====================

    private void mockEmptyDatabaseQuery() {
        when(deviceCertificateService.list(any(LambdaQueryWrapper.class)))
            .thenReturn(new ArrayList<>());
    }

    private void mockEmptyDatabaseQueryForAsync() {
        // 为异步方法模拟数据库查询，返回只包含ID的空列表
        lenient().when(deviceCertificateService.list(any(LambdaQueryWrapper.class)))
            .thenReturn(new ArrayList<>());
    }

    private void mockDatabaseQueryWithResults(List<DeviceCertificate> certificates) {
        when(deviceCertificateService.list(any(LambdaQueryWrapper.class)))
            .thenReturn(certificates);
    }

    @SuppressWarnings("unchecked")
    private void mockAwsIotServiceCalls() {
        lenient().when(awsIotService.getAwsIot()).thenReturn(awsIot);
        lenient().when(awsIot.createKeysAndCertificate(any(CreateKeysAndCertificateRequest.class)))
            .thenReturn(certResult);
        lenient().when(certResult.getCertificateId()).thenReturn(TEST_CERTIFICATE_ID);
        lenient().when(certResult.getCertificatePem()).thenReturn(TEST_CERTIFICATE_PEM);
        lenient().when(certResult.getCertificateArn()).thenReturn(TEST_CERTIFICATE_ARN);
        lenient().when(certResult.getKeyPair()).thenReturn(keyPair);
        lenient().when(keyPair.getPrivateKey()).thenReturn(TEST_PRIVATE_KEY);
        
        // Mock insert method - 修复返回类型为int
        lenient().when(deviceCertificateService.getBaseMapper().insert(any(DeviceCertificate.class))).thenReturn(1);
    }

    private List<DeviceCertificate> createExistingCertificates() {
        List<DeviceCertificate> certificates = new ArrayList<>();
        certificates.add(createDeviceCertificate(TEST_DEVICE_ID_1));
        certificates.add(createDeviceCertificate(TEST_DEVICE_ID_2));
        return certificates;
    }

    private DeviceCertificate createDeviceCertificate(String deviceId) {
        DeviceCertificate certificate = new DeviceCertificate();
        certificate.setId(TEST_ID);
        certificate.setDeviceId(deviceId);
        certificate.setCertificateId(TEST_CERTIFICATE_ID);
        certificate.setCertificatePem(TEST_CERTIFICATE_PEM);
        certificate.setCertificateArn(TEST_CERTIFICATE_ARN);
        certificate.setPrivateKey(TEST_PRIVATE_KEY);
        return certificate;
    }
}