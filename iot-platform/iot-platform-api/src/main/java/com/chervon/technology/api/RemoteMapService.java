package com.chervon.technology.api;

import com.chervon.technology.api.vo.map.DeviceAreaVo;
import com.chervon.technology.api.vo.map.DeviceMapVo;
import com.chervon.technology.api.vo.map.DevicePathNewVo;
import com.chervon.technology.api.vo.map.DevicePathVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/15 20:37
 */
public interface RemoteMapService {

    /**
     * 根据设备id获取地图信息
     *
     * @param deviceId 设备id
     * @return 地图信息
     */
    DeviceMapVo loadMapByDeviceId(String deviceId);

    /**
     * 根据设备id获取路径信息
     *
     * @param deviceId 设备id
     * @return 路径信息
     */
    DevicePathVo loadPathByDeviceId(String deviceId);

    /**
     * 区域操作
     *
     * @param deviceId  设备id
     * @param areaId    区域id
     * @param name      区域名称
     * @param level     区域级别
     * @param operation 操作类型
     */
    void areaOperation(String deviceId, String areaId, String name, String level, String operation);

    /**
     * 区域查询
     *
     * @param deviceId 设备id
     * @return 区域列表
     */
    List<DeviceAreaVo> areaList(String deviceId);

    /**
     * 获取设备轨迹数据
     *
     * @param deviceId 设备id
     * @param pathIds  轨迹id集合
     * @return 轨迹数据集合
     */
    List<DevicePathNewVo> loadPathByDeviceId(String deviceId, List<String> pathIds);
}
