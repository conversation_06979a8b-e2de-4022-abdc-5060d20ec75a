package com.chervon.technology.api.todevice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/8 10:21
 */
@Data
@ApiModel(description = "设备端上次地图入参")
@AllArgsConstructor
public class DeviceMapUploadDto implements Serializable {

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "是否是整图 true  false")
    private Boolean complete;

    @ApiModelProperty(value = "最新整图的md5摘要")
    private String md5;

    @ApiModelProperty(value = "token")
    private String token;

}
