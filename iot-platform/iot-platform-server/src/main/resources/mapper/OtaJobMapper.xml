<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.OtaJobMapper">
    <resultMap id="BaseResultMap" type="com.chervon.technology.api.vo.ota.JobDetailVo">
        <id column="id" jdbcType="BIGINT" property="jobId"/>
        <id column="product_id" jdbcType="BIGINT" property="productId"/>
        <id column="categoryName" jdbcType="VARCHAR" property="categoryName"/>
        <id column="brandName" jdbcType="VARCHAR" property="brandName"/>
        <id column="productModel" jdbcType="VARCHAR" property="productModel"/>
        <id column="commodity_model" jdbcType="VARCHAR" property="commodityModel"/>
        <id column="technology_version" jdbcType="VARCHAR" property="technologyVersion"/>
        <id column="package_count" jdbcType="VARCHAR" property="packageCount"/>
        <collection property="firmwares" column="id"
                    ofType="com.chervon.technology.api.vo.ota.FirmwareVo"
                    select="selectFirmwaresByJobId">
        </collection>
    </resultMap>
    <resultMap id="ReleaseDetailMap" type="com.chervon.technology.api.vo.ota.JobReleaseDetailVo">
        <id column="id" jdbcType="BIGINT" property="jobId"/>
        <id column="product_id" jdbcType="BIGINT" property="productId"/>
        <id column="categoryName" jdbcType="VARCHAR" property="categoryName"/>
        <id column="brandName" jdbcType="VARCHAR" property="brandName"/>
        <id column="productModel" jdbcType="VARCHAR" property="productModel"/>
        <id column="commodity_model" jdbcType="VARCHAR" property="commodityModel"/>
        <id column="technology_version" jdbcType="VARCHAR" property="technologyVersion"/>
        <id column="custom_version" jdbcType="VARCHAR" property="customVersion"/>
        <id column="package_count" jdbcType="VARCHAR" property="packageCount"/>
        <id column="release_content" jdbcType="VARCHAR" property="releaseContent"/>
        <id column="start_type" jdbcType="INTEGER" property="startType"/>
        <id column="start_zone" jdbcType="VARCHAR" property="startZone"/>
        <id column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <id column="end_type" jdbcType="INTEGER" property="endType"/>
        <id column="end_zone" jdbcType="VARCHAR" property="endZone"/>
        <id column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <collection property="firmwares" column="id"
                    ofType="com.chervon.technology.api.vo.ota.FirmwareVo"
                    select="selectFirmwaresByJobId">
        </collection>
        <collection property="productGroupNames" column="id"
                    ofType="java.lang.String"
                    select="selectProductGroupNamesByJobId">
        </collection>
        <collection property="testGroupNames" column="id"
                    ofType="java.lang.String"
                    select="selectTestGroupNamesByJobId">
        </collection>
    </resultMap>
    <select id="pageRelease" resultType="com.chervon.technology.api.vo.ota.JobReleaseListVo">
        select OJ.id, P.id productId, P.model productModel, P.category_id categoryName,
        P.brand_id brandName,
        P.commodity_model commodityModel, OJ.package_count packageCount,
        OJ.release_status releaseStatus, OJ.description, P.brand_id brandName,
        OJ.apply_time applyTime, OJ.apply_by applyBy,
        OJ.ensured_time ensuredTime, OJ.ensured_by ensuredBy
        from iot_platform.ota_job OJ
        left join iot_platform.product P on P.id = OJ.product_id
        <where>
            <if test="search.productId != null and search.productId != ''">
                OJ.product_id like concat('%', #{search.productId}, '%')
            </if>
            <if test="search.categoryId != null">
                and P.category_id = #{search.categoryId}
            </if>
            <if test="search.brandId != null">
                and P.brand_id = #{search.brandId}
            </if>
            <if test="search.productModel != null">
                and P.model like concat('%', #{search.productModel}, '%')
            </if>
            <if test="search.commodityModel != null and search.commodityModel != ''">
                and P.commodity_model like concat('%', #{search.commodityModel}, '%')
            </if>
            <if test="search.jobId != null">
                and OJ.id like concat('%', #{search.jobId}, '%')
            </if>
            <if test="search.releaseStatus != null">
                and OJ.release_status = #{search.releaseStatus}
            </if>
            <!--创建时间更新时间区间搜索-->
            <if test="search.createStartTime != null and search.createStartTime != ''">
                and OJ.create_time &gt;= #{search.createStartTime}
            </if>
            <if test="search.createEndTime != null and search.createEndTime != ''">
                and OJ.create_time &lt;= #{search.createEndTime}
            </if>
            <if test="search.updateStartTime != null and search.updateStartTime != ''">
                and OJ.update_time &gt;= #{search.updateStartTime}
            </if>
            <if test="search.updateEndTime != null and search.updateEndTime != ''">
                and OJ.update_time &lt;= #{search.updateEndTime}
            </if>
            <!--申请人,审批人模糊搜索-->
            <if test="search.applyBy != null and search.applyBy != ''">
                and OJ.apply_by like concat('%', #{search.applyBy}, '%')
            </if>
            <if test="search.approveBy != null and search.approveBy != ''">
                and OJ.ensured_by like concat('%', #{search.approveBy}, '%')
            </if>
            <!--申请时间,审批时间区间搜索-->
            <if test="search.applyStartTime != null and search.applyStartTime != ''">
                and OJ.apply_time &gt;= #{search.applyStartTime}
            </if>
            <if test="search.applyEndTime != null and search.applyEndTime != ''">
                and OJ.apply_time &lt;= #{search.applyEndTime}
            </if>
            <if test="search.approveStartTime != null and search.approveStartTime != ''">
                and OJ.ensured_time &gt;= #{search.approveStartTime}
            </if>
            <if test="search.approveEndTime != null and search.approveEndTime != ''">
                and OJ.ensured_time &lt;= #{search.approveEndTime}
            </if>
            and OJ.develop_status = 'CLOSED'
            and OJ.release_status != 'RELEASE_WAITING'
            and OJ.is_deleted = 0 and P.is_deleted = 0
        </where>
        order by OJ.create_time desc
    </select>

    <select id="listRelease" resultType="com.chervon.technology.api.vo.ota.JobReleaseListVo">
        select OJ.id, P.id productId, P.model productModel, P.category_id categoryName,
        P.brand_id brandName,
        P.commodity_model commodityModel, OJ.package_count packageCount,
        OJ.release_status releaseStatus, OJ.description, P.brand_id brandName,
        OJ.apply_time applyTime, OJ.apply_by applyBy,
        OJ.ensured_time ensuredTime, OJ.ensured_by ensuredBy
        from ota_job OJ
        left join product P on P.id = OJ.product_id
        <where>
            <if test="search.productId != null and search.productId != ''">
                OJ.product_id like concat('%', #{search.productId}, '%')
            </if>
            <if test="search.categoryId != null">
                and P.category_id = #{search.categoryId}
            </if>
            <if test="search.brandId != null">
                and P.brand_id = #{search.brandId}
            </if>
            <if test="search.productModel != null">
                and P.model like concat('%', #{search.productModel}, '%')
            </if>
            <if test="search.commodityModel != null and search.commodityModel != ''">
                and P.commodity_model like concat('%', #{search.commodityModel}, '%')
            </if>
            <if test="search.jobId != null">
                and OJ.id like concat('%', #{search.jobId}, '%')
            </if>
            <if test="search.releaseStatus != null">
                and OJ.release_status = #{search.releaseStatus}
            </if>
            <!--创建时间更新时间区间搜索-->
            <if test="search.createStartTime != null and search.createStartTime != ''">
                and OJ.create_time &gt;= #{search.createStartTime}
            </if>
            <if test="search.createEndTime != null and search.createEndTime != ''">
                and OJ.create_time &lt;= #{search.createEndTime}
            </if>
            <if test="search.updateStartTime != null and search.updateStartTime != ''">
                and OJ.update_time &gt;= #{search.updateStartTime}
            </if>
            <if test="search.updateEndTime != null and search.updateEndTime != ''">
                and OJ.update_time &lt;= #{search.updateEndTime}
            </if>
            <!--申请人,审批人模糊搜索-->
            <if test="search.applyBy != null and search.applyBy != ''">
                and OJ.apply_by like concat('%', #{search.applyBy}, '%')
            </if>
            <if test="search.approveBy != null and search.approveBy != ''">
                and OJ.ensured_by like concat('%', #{search.approveBy}, '%')
            </if>
            <!--申请时间,审批时间区间搜索-->
            <if test="search.applyStartTime != null and search.applyStartTime != ''">
                and OJ.apply_time &gt;= #{search.applyStartTime}
            </if>
            <if test="search.applyEndTime != null and search.applyEndTime != ''">
                and OJ.apply_time &lt;= #{search.applyEndTime}
            </if>
            <if test="search.approveStartTime != null and search.approveStartTime != ''">
                and OJ.ensured_time &gt;= #{search.approveStartTime}
            </if>
            <if test="search.approveEndTime != null and search.approveEndTime != ''">
                and OJ.ensured_time &lt;= #{search.approveEndTime}
            </if>
            and OJ.develop_status = 'CLOSED'
            and OJ.release_status != 'RELEASE_WAITING'
            and OJ.is_deleted = 0 and P.is_deleted = 0
        </where>
        order by OJ.create_time desc
    </select>

    <select id="listReleaseByStatusList" resultType="com.chervon.technology.api.vo.ota.JobReleaseListVo">
        select id, product_id productId,release_status releaseStatus
        from ota_job
        <where>
            <if test="productId != null">
                product_id = #{productId}
            </if>
            and release_status in
            <foreach collection="releaseStatusList" item="releaseStatus" index="index" open="(" close=")" separator=",">
                #{releaseStatus}
            </foreach>
            and is_deleted = 0
        </where>
    </select>

    <select id="getDetail" resultMap="BaseResultMap">
        select OJ.id,
               OJ.product_id,
               P.category_id as categoryName,
               P.model       as productModel,
               commodity_model,
               P.brand_id       brandName,
               OJ.technology_version,
               OJ.package_count
        from ota_job OJ
                 left join product P on P.id = OJ.product_id
        where OJ.id = #{jobId}
          and OJ.is_deleted = 0
          and P.is_deleted = 0;
    </select>

    <select id="selectFirmwaresByJobId" resultType="com.chervon.technology.api.vo.ota.FirmwareVo">
        select COM.component_no   as componentNo,
               COM.component_type as componentType,
               COM.component_name as componentName,
               F.package_name     as packageName,
               F.package_version  as packageVersion,
               F.display_version  as displayVersion,
               F.size,
               F.minimum_version  as minimumVersion,
               F.package_type     as packageType,
               F.package_key as 'key', F.order_num as orderNum,
               F.hash
        from firmware F
                 left join component COM on COM.component_no = F.component_no
        where F.job_id = #{jobId}
          and F.is_deleted = 0
          and COM.is_deleted = 0;
    </select>
    <select id="getReleaseDetail" resultMap="ReleaseDetailMap">
        select OJ.id,
               OJ.product_id,
               P.category_id as categoryName,
               P.model       as productModel,
               commodity_model,
               P.brand_id       brandName,
               OJ.technology_version,
               OJ.custom_version,
               OJ.start_type,
               OJ.start_zone,
               OJ.start_time,
               OJ.end_type,
               OJ.end_zone,
               OJ.end_time,
               OJ.release_content,
               OJ.package_count
        from ota_job OJ
                 left join product P on P.id = OJ.product_id
        where OJ.id = #{jobId}
          and OJ.is_deleted = 0
          and P.is_deleted = 0;
    </select>
    <select id="selectTestGroupNamesByJobId" resultType="java.lang.String">
        select group_name
        from ota_job_group
        where group_type = 0
          and job_id = #{jobId}
          and is_deleted = 0
    </select>
    <select id="selectProductGroupNamesByJobId" resultType="java.lang.String">
        select group_name
        from ota_job_group
        where group_type = 1
          and job_id = #{jobId}
          and is_deleted = 0
    </select>

</mapper>
