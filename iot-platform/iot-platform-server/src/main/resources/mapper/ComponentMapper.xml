<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.ComponentMapper">
  <select id="getComponentVoByPid"
    resultType="com.chervon.technology.domain.vo.component.ComponentVo">
    select  pc.id as id,pc.product_id as productId,
            pc.component_no as componentNo,
            c.component_name as componentName,
            c.component_type as componentType,
            pc.software_coding as softwareCoding
    from product_component pc
           join component c
                on c.component_no = pc.component_no
    where pc.product_id = #{pid}
      and pc.is_deleted = 0
      and c.is_deleted = 0 order by pc.create_time desc
  </select>


  <select id="getListByComponentNo" resultType="java.lang.Long">
    select product_id
    from product_component
    where component_no = #{componentNo}
      and is_deleted = 0
  </select>


  <select id="check" resultType="com.chervon.technology.domain.vo.component.ComponentCheckVo">
    select component_name, component_type
    from component C
           left join product_component PC on PC.component_no = C.component_no
    where PC.product_id = #{checkComponentDto.productId}
      and C.component_no = #{checkComponentDto.componentNo}
      and C.is_deleted = 0
      and PC.is_deleted = 0
  </select>


</mapper>
