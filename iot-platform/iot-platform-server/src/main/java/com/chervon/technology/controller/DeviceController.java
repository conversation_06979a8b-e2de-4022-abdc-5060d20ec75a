package com.chervon.technology.controller;

import com.chervon.common.core.constant.CacheConstants;
import com.chervon.common.core.domain.R;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;
import com.chervon.technology.api.dto.DeviceIdDto;
import com.chervon.technology.api.dto.DeviceRegisterDto;
import com.chervon.technology.api.todevice.IotDeviceVerifyDto;
import com.chervon.technology.api.todevice.DeviceFlowDto;
import com.chervon.technology.api.todevice.DeviceMapUploadDto;
import com.chervon.technology.api.todevice.DeviceTimeDto;
import com.chervon.technology.api.todevice.DeviceTimeVo;
import com.chervon.technology.api.todevice.IotDeviceVerifyVo;
import com.chervon.technology.api.todevice.DeviceFlowVo;
import com.chervon.technology.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 设备相关接口，给设备提供接口
 */

@Api(tags = "设备相关接口，给设备提供接口")
@RestController
@RequestMapping("/device")
@Slf4j
public class DeviceController {

    @Autowired
    private DeviceService deviceService;


    /**
     * 设备验签
     *
     * @param dto 设备验签Dto
     * @return token
     */
    @ApiOperation(value = "设备验签")
    @PostMapping("/verify")
    public R<IotDeviceVerifyVo> verify(@Validated @RequestBody IotDeviceVerifyDto dto) {
        return R.ok(deviceService.deviceVerify(dto));
    }

    /**
     * 设备注册,返回证书信息
     *
     * @param deviceRegisterDto 设备
     */
    @PostMapping("/register")
    @ApiOperation("设备注册,返回证书信息")
    public R<IotDeviceCertVo> register(@Validated @RequestBody DeviceRegisterDto deviceRegisterDto,@RequestHeader("token") String token) {
        String deviceId = RedisUtils.getCacheObject(CacheConstants.IOT_PLATFORM_DEVICE_VERIFY_TOKEN + token);
        String registerDeviceId=deviceRegisterDto.getDeviceId();
        log.info("DeviceController register token is {},deviceId is {},register deviceId is{}",token,deviceId,registerDeviceId);
        return R.ok(deviceService.syncDeviceRegister(deviceRegisterDto));
    }

    /**
     * 检查设备是否已注册
     *
     * @param deviceIdDto 设备Id
     */
    @PostMapping("/check/registered")
    @ApiOperation("检查设备是否已注册")
    public R<Boolean> checkRegistered(@Validated @RequestBody DeviceIdDto deviceIdDto) {
        return R.ok(deviceService.checkRegistered(deviceIdDto));
    }

    /**
     * 时间同步
     *
     * @param deviceTime 设备时间
     * @return 设备时间Vo
     */
    @PostMapping("/timeservice")
    @ApiOperation("时间同步")
    public R<DeviceTimeVo> timeService(@Validated @RequestBody DeviceTimeDto deviceTime) {
        return R.ok(deviceService.timeService(deviceTime));
    }

    /**
     * 获取流量情况
     *
     * @param deviceFlowDto 设备流量DTO
     * @return 设备列量Vo
     */
    @PostMapping("/flow")
    @ApiOperation("获取流量情况")
    public R<DeviceFlowVo> flow(@Validated @RequestBody DeviceFlowDto deviceFlowDto) {
        return R.ok(deviceService.flow(deviceFlowDto));
    }

    /**
     * R项目上传地图文件
     * @param file     上传的地图文件
     * @param deviceId 设备id
     * @param complete 是否是整图
     * @param md5      摘要
     * @return 地图信息Vo
     */
    @ApiOperation("R项目上传地图文件")
    @PostMapping("/map/upload")
    public R<Void> uploadMap(@RequestParam(value = "file") MultipartFile file
            , @RequestParam(value = "deviceId") String deviceId
            , @RequestParam(value = "complete") Boolean complete
            , @RequestParam(value = "md5") String md5
            , @RequestHeader("token") String token) throws Exception {
        deviceService.uploadMap(new DeviceMapUploadDto(deviceId, complete, md5,token), file);
        return R.ok();
    }

//    /**
//     * R项目上传地图文件(带返回结果)
//     *
//     * @param file     上传的地图文件
//     * @param deviceId 设备id
//     * @param complete 是否是整图
//     * @param md5      摘要
//     * @return 地图信息Vo
//     */
//    @ApiOperation("R项目上传地图文件")
//    @PostMapping("/map/uploadV2")
//    public R<String> uploadMapWithResult(@RequestParam(value = "file") MultipartFile file
//            , @RequestParam(value = "deviceId") String deviceId
//            , @RequestParam(value = "complete") Boolean complete
//            , @RequestParam(value = "md5") String md5) throws Exception {
//        final String result = deviceService.uploadMapWithResult(new DeviceMapUploadDto(deviceId, complete, md5), file);
//        return R.ok(result);
//    }

}
