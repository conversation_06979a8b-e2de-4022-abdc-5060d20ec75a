package com.chervon.technology.rpc;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.technology.api.RemoteMapService;
import com.chervon.technology.api.vo.map.DeviceAreaVo;
import com.chervon.technology.api.vo.map.DeviceMapVo;
import com.chervon.technology.api.vo.map.DevicePathNewVo;
import com.chervon.technology.api.vo.map.DevicePathVo;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.domain.dataobject.DeviceArea;
import com.chervon.technology.domain.dataobject.DeviceMap;
import com.chervon.technology.service.DeviceAreaService;
import com.chervon.technology.service.DeviceMapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

import static com.chervon.technology.api.exception.TechnologyErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022/8/15 20:42
 */
@DubboService
@Slf4j
@Service
public class RemoteMapServiceImpl implements RemoteMapService {

    @Autowired
    private DeviceMapService deviceMapService;

    @Autowired
    private S3Util s3Util;

    @Autowired
    private AwsProperties awsProperties;

    @Autowired
    private DeviceAreaService deviceAreaService;

    private static final String PRIMARY = "primary";

    private static final String SECONDARY = "secondary";

    private static final String STORE = "store";

    private static final String DELETE = "delete";

    @Override
    public DeviceMapVo loadMapByDeviceId(String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_MAP_NOT_EXIST);
        }
        DeviceMap one = deviceMapService.getOne(new LambdaQueryWrapper<DeviceMap>().eq(DeviceMap::getDeviceId, deviceId)
                .orderByDesc(DeviceMap::getUpdateTime)
                .last("limit 1"));
        if (one == null) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_MAP_NOT_EXIST);
        }
        DeviceMapVo res = new DeviceMapVo();
        res.setDeviceId(deviceId);
        try {
            Date expiration = new Date();
            long expTimeMillis = expiration.getTime() + 1000 * 60 * 10;
            expiration.setTime(expTimeMillis);
            String preSignedUrl = s3Util.getPreSignedGetPrivateUrl(awsProperties.getDefaultBucket().getName(), one.getFileKey(), expiration);
            log.info("loadMapByDeviceId preSignedUrl:{}", preSignedUrl);
            res.setUrl(preSignedUrl);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("loadMapByDeviceId res:{}", res);
        return res;
    }

    @Override
    public DevicePathVo loadPathByDeviceId(String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_PATH_NOT_EXIST);
        }

        DevicePathVo res = new DevicePathVo();
        res.setDeviceId(deviceId);
        StopWatch sw = new StopWatch();
        sw.start("取H");
        String h = RedisUtils.getCacheObject("iot:path:deviceId:" + deviceId + ":h");
        if (h != null) {
            res.setH(JSONUtil.toBean(h, new TypeReference<List<String>>() {
            }, true));
        }
        sw.stop();
        sw.start("取V");
        List<List<String>> v = RedisUtils.getCacheList("iot:path:deviceId:" + deviceId + ":v");
        if (v != null) {
            Map<Long, List<String>> map = new HashMap<>();
            v.forEach(e -> {
                if (e != null && e.size() == 3) {
                    try {
                        map.put(Long.parseLong(e.get(2)), e);
                    } catch (Exception ex) {
                        log.error("v order parse long error", ex);
                    }
                }
            });
            List<List<String>> lists = new ArrayList<>(map.values());
            lists.sort(Comparator.comparing(e -> Long.parseLong(e.get(2))));
            res.setV(lists);
        }
        sw.stop();
        log.error("app stop watch:{}", sw.prettyPrint());
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void areaOperation(String deviceId, String areaId, String name, String level, String operation) {
        if (StringUtils.isBlank(operation)) {
            return;
        }
        if (StringUtils.isBlank(deviceId)) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_AREA_DEVICE_ID_BLANK);
        }
        if (StringUtils.isBlank(areaId)) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_AREA_BLANK);
        }
        switch (operation) {
            case STORE:
                if (StringUtils.isBlank(name)) {
                    throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_AREA_NAME_BLANK);
                }
                if (StringUtils.isBlank(level) || !Arrays.asList(PRIMARY, SECONDARY).contains(level)) {
                    throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_AREA_LEVEL_ERROR);
                }
                DeviceArea one;
                if (PRIMARY.equals(level)) {
                    one = deviceAreaService.getOne(new LambdaQueryWrapper<DeviceArea>().eq(DeviceArea::getDeviceId, deviceId).eq(DeviceArea::getLevel, level));
                    if (one != null && !areaId.equals(one.getAreaId())) {
                        throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_AREA_LEVEL_PRIMARY_ONLY_ONE);
                    }
                } else {
                    one = deviceAreaService.getOne(new LambdaQueryWrapper<DeviceArea>()
                            .eq(DeviceArea::getDeviceId, deviceId)
                            .eq(DeviceArea::getAreaId, areaId));
                }
                DeviceArea deviceArea = new DeviceArea();
                if (one == null) {
                    deviceArea.setDeviceId(deviceId);
                    deviceArea.setAreaId(areaId);
                    deviceArea.setName(name);
                    deviceArea.setLevel(level);
                    deviceAreaService.save(deviceArea);
                } else {
                    deviceArea.setId(one.getId());
                    deviceArea.setName(name);
                    deviceAreaService.updateById(deviceArea);
                }
                break;
            case DELETE:
                deviceAreaService.remove(new LambdaQueryWrapper<DeviceArea>().eq(DeviceArea::getDeviceId, deviceId).eq(DeviceArea::getAreaId, areaId));
                break;
            default:
                break;
        }
    }

    @Override
    public List<DeviceAreaVo> areaList(String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_AREA_DEVICE_ID_BLANK);
        }
        List<DeviceArea> list = deviceAreaService.list(new LambdaQueryWrapper<DeviceArea>().eq(DeviceArea::getDeviceId, deviceId));
        return list.stream().map(e -> {
            DeviceAreaVo vo = new DeviceAreaVo();
            BeanUtils.copyProperties(e, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DevicePathNewVo> loadPathByDeviceId(String deviceId, List<String> pathIds) {
        if (StringUtils.isBlank(deviceId)) {
            throw ExceptionMessageUtil.getException(TECHNOLOGY_DEVICE_PATH_NOT_EXIST);
        }
        List<DevicePathNewVo> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(pathIds)) {
            return res;
        }
        for (String pId : pathIds) {
            // pathId和areaId的映射
            String areaId = RedisUtils.getCacheObject("iot:r:device:" + deviceId + ":pathId:" + pId);
            if (areaId == null) {
                continue;
            }
            // areaId、pathId和t集合的映射
            List<String> tt = RedisUtils.getCacheList("iot:r:device:" + deviceId + ":areaId:" + areaId + ":pathId:" + pId);
            if (tt != null) {
                DevicePathNewVo vo = new DevicePathNewVo();
                vo.setDeviceId(deviceId);
                vo.setAreaId(areaId);
                vo.setPathId(pId);
                // 去掉前32位，包括路径id和区域id
                vo.setT(tt.stream().map(e -> e.substring(32)).collect(Collectors.toList()));
                res.add(vo);
            }
        }
        return res;
    }

}
