package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;
import com.chervon.iot.middle.api.vo.shadow.IotDeviceShadowItemVo;
import com.chervon.technology.api.dto.DeviceEditStatusDto;
import com.chervon.technology.api.dto.DeviceIdDto;
import com.chervon.technology.api.dto.DeviceRegisterDto;
import com.chervon.technology.api.todevice.*;
import com.chervon.technology.api.todevice.DeviceTimeVo;
import com.chervon.technology.domain.dataobject.Device;
import com.chervon.technology.domain.dataobject.Product;
import com.chervon.technology.domain.dto.*;
import com.chervon.technology.domain.dto.device.*;
import com.chervon.technology.domain.dto.ota.ComponentOtaListDto;
import com.chervon.technology.domain.dto.ota.DeviceOtaDetailDto;
import com.chervon.technology.domain.dto.ota.DeviceOtaListDto;
import com.chervon.technology.domain.vo.component.ComponentVo;
import com.chervon.technology.domain.vo.device.*;
import com.chervon.technology.domain.vo.ota.ComponentOtaListVo;
import com.chervon.technology.domain.vo.ota.DeviceOtaDetailVo;
import com.chervon.technology.domain.vo.ota.DeviceOtaHistoryVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022-07-06
 */
public interface DeviceService extends IService<Device> {

    /**
     * 根据设备名称修改
     *
     * @param deviceId 设备名称
     * @return 设备信息
     */
    Device findByDeviceId(String deviceId);

    /**
     * 根据设备Sn获取设备信息
     *
     * @param sn 设备sn
     * @return 设备信息
     */
    Device findBySn(String sn);

    /**
     * 编辑设备
     *
     * @param deviceEdit 设备编辑Dto
     */
    void editDevice(DeviceEditDto deviceEdit);

    /**
     * 设备同步时间
     *
     * @param deviceTimeDto 设备同步时间信息
     * @return 设备同步时间Vo
     */
    DeviceTimeVo timeService(DeviceTimeDto deviceTimeDto);

    /**
     * 根据设备deviceId获取设备详情
     *
     * @param deviceId 设备deviceId
     * @return 品牌信息
     */
    DeviceDetailVo getDetail(String deviceId);

    /**
     * 根据设备deviceId 的获取ProductId
     *
     * @param deviceId 设备deviceId
     * @return ProductId
     */
    Long getProductId(String deviceId);

    /**
     * 分页搜索设备列表
     *
     * @param searchDevice 查询设备Dto
     * @return 设备Vo分页结果
     */
    PageResult<DeviceListVo> getDevicePage(SearchDeviceDto searchDevice);

    /**
     * 搜索设备列表 查询条件的所有数据-不分页
     *
     * @param searchDevice 查询设备Dto
     * @return 设备Vo分页结果
     */
    List<DeviceExcel> getDeviceList(SearchDeviceDto searchDevice);

    /**
     * 编辑设备状态
     *
     * @param deviceEditStatusDto 编辑设备状态Dto
     */
    void editDeviceStatus(DeviceEditStatusDto deviceEditStatusDto);

    /**
     * 通过设备ID获取总成零件列表
     *
     * @param dto 设备Id
     * @return 总成零件列表
     */
    PageResult<ComponentVo> getComponentList(DeviceComponentListDto dto);

    /**
     * 导出设备信息表格文件
     *
     * @param searchDeviceDto 查询条件
     * @param response        http响应体
     */
    void export(SearchDeviceDto searchDeviceDto, HttpServletResponse response) throws IOException;

    /**
     * 获取拓扑设备列表
     *
     * @param deviceTopologyQueryDto 查询拓扑设备dto
     * @return 拓扑设备分页结果
     */
    PageResult<DeviceTopologyVo> getTopologicalDeviceList(DeviceTopologyQueryDto deviceTopologyQueryDto);

    /**
     * 分页查询设备属性类功能列表
     *
     * @param thingModelListDto 设备Id+分页Dto
     * @return 分页结果
     */
    PageResult<IotDeviceShadowItemVo> pageDeviceProperty(ThingModelListDto thingModelListDto);

    /**
     * 分页查询设备服务类功能列表
     *
     * @param thingModelListDto 设备Id+分页Dto
     * @return 分页结果
     */
    PageResult<IotDeviceShadowItemVo> pageDeviceService(ThingModelListDto thingModelListDto);

    /**
     * 分页查询设备事件类功能列表
     *
     * @param thingModelListDto 设备Id+分页Dto
     * @return 分页结果
     */
    PageResult<IotDeviceShadowItemVo> pageDeviceEvent(ThingModelListDto thingModelListDto);

    /**
     * 根据设备Id+功能Id+数据类型获取最近一次日志
     *
     * @param dto 查询条件
     * @return JSON字符串
     */
    Object getDeviceFunctionLog(DeviceFunctionLogDto dto);

    /**
     * @param deviceId:
     * @param status:
     * <AUTHOR>
     * @date 13:49 2022/7/16
     **/
    boolean updateOnlineStatus(String deviceId, String status);

    /**
     * 设备在线状态判断
     * @param deviceId
     * @return
     */
    Boolean isOnline(String deviceId);

    /**
     * 设备注册
     *
     * @param deviceReg 设备注册Dto
     * @return 注册结果
     */
    IotDeviceCertVo deviceRegister(DeviceRegisterDto deviceReg);

    /**
     * 设备注册-同步
     * @param deviceRegisterDto
     * @return
     */
    IotDeviceCertVo syncDeviceRegister(DeviceRegisterDto deviceRegisterDto);

    /**
     * 虚拟设备注册
     *
     * @param deviceReg 设备注册Dto
     * @return 注册结果
     */
    IotDeviceCertVo deviceRegisterVirtual(DeviceRegisterDto deviceReg);

    /**
     * 非IOT设备注册
     *
     * @param deviceRegisterDto 设备注册Dto
     */
    void deviceRegisterNoIot(DeviceRegisterDto deviceRegisterDto);

    /**
     * 检查设备是否已注册
     *
     * @param deviceIdDto 设备Id
     * @return 是否已注册
     */
    Boolean checkRegistered(DeviceIdDto deviceIdDto);

    /**
     * 设备验签
     *
     * @param dto 设备Id密文+私钥
     * @return token
     */
    IotDeviceVerifyVo deviceVerify(IotDeviceVerifyDto dto);

    /**
     * 获取设备当月使用流量情况
     *
     * @param deviceFlowDto iccidDto
     * @return 流量Vo
     */
    DeviceFlowVo flow(DeviceFlowDto deviceFlowDto);

    /**
     * 获取设备指定字段 "deviceId" "productModel" "isOnline"
     *
     * @param deviceIds:
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 20:32 2022/7/28
     **/
    List<Map<String, Object>> listMapByIds(List<String> deviceIds);

    /**
     * R项目上传地图
     *
     * @param req  入参
     * @param file 地图文件
     */
    void uploadMap(DeviceMapUploadDto req, MultipartFile file) throws Exception;

    /**
     * R项目上传地图测试
     *
     * @param req  入参
     * @param file 地图文件
     */
    String uploadMapWithResult(DeviceMapUploadDto req, MultipartFile file) throws Exception;
    /**
     * 获取设备当前固件显示版本和技术版本
     *
     * @param deviceId:
     * @return java.lang.String
     * <AUTHOR>
     * @date 11:38 2022/8/16
     **/
    Device getCurrentFirmwareVersion(String deviceId);

    /**
     * 更新设备显示版本
     *
     * @param deviceId
     */
    void updateDeviceVersionV2(Long jobId, String deviceId, String customVersion);
    /**
     * 更新设备固件版本号
     *
     * @param deviceId:
     * @param versionMap:
     * @return void
     * <AUTHOR>
     * @date 9:46 2022/8/17
     **/
    void updateFirmwareVersion(String deviceId, Map<String, String> versionMap);


    /**
     * 分页获取设备升级记录
     *
     * @param deviceOtaListDto 获取固件升级记录Dto
     * @return 固件升级记录Vo列表
     */
    PageResult<DeviceOtaHistoryVo> getOtaRecord(DeviceOtaListDto deviceOtaListDto);

    /**
     * 获取设备升级记录详情
     *
     * @param detailDto 获取设备升级记录详情dto
     * @return 固件升级记录Vo列表
     */
    PageResult<DeviceOtaDetailVo> getOtaDetail(DeviceOtaDetailDto detailDto);

    /**
     * 查看总成固件升级记录
     *
     * @param dto
     * @return 总成固件升级记录列表
     */
    PageResult<ComponentOtaListVo> getComponentOtaList(ComponentOtaListDto dto);

    /**
     * 更新device表中的sn信息
     *
     * @param dto
     */
    void batchUpdateDeviceSnByDeviceId(List<DeviceUpdateDto> dto);

    /**
     * 根据deviceId更新device表中的sn信息
     *
     * @param deviceId
     * @param sn
     */
    void updateDeviceSnByDeviceId(String deviceId, String sn);

    /**
     * 组装自定义版本号
     *
     * @param versionList 版本号列表
     * @param createTime 创建时间
     * @return 自定义版本号
     */
    String assembleCustomVersion(List<String> versionList, LocalDateTime createTime);


    /**
     *  多码关系表修复设备表 SN
     */
    void fixMissingDeviceSnFromDeviceCode();
}
