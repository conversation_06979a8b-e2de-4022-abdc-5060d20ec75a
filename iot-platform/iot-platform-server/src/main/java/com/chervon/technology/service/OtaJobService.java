package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.iot.middle.api.pojo.ota.JobDocument;
import com.chervon.technology.api.dto.ota.JobReleaseListDto;
import com.chervon.technology.api.enums.OtaJobReleaseStatus;
import com.chervon.technology.api.toruleengine.CheckJobDto;
import com.chervon.technology.api.toruleengine.JobActionVo;
import com.chervon.technology.api.toruleengine.JobStatusRuleDto;
import com.chervon.technology.api.toruleengine.OtaResultDto;
import com.chervon.technology.api.vo.ota.JobDetailVo;
import com.chervon.technology.api.vo.ota.JobReleaseDetailVo;
import com.chervon.technology.api.vo.ota.JobReleaseListVo;
import com.chervon.technology.domain.dto.firmware.ListVersionDto;
import com.chervon.technology.domain.dto.ota.*;
import com.chervon.technology.domain.entity.OtaJob;
import com.chervon.technology.domain.vo.ota.JobListVo;
import com.chervon.technology.domain.vo.ota.PreSignedUrlVo;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 升级任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
public interface OtaJobService extends IService<OtaJob> {

    /**
     * 获取固件包最新的下载链接请求
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @param deviceId:
     * @param packageKeys:
     * @return: void
     **/
    void getUrl(String deviceId, List<String> packageKeys) throws IOException;

    /**
     * 更新是否允许升级
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @param deviceId:
     * @param jobId:
     * @param jobActionVo:
     * @return: void
     **/
    void reportJobAction(String deviceId, Long jobId, JobActionVo jobActionVo);

    /**
     * 更新是否允许升级
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @param deviceId:
     * @param jobId:
     * @return: void
     **/
    void getJobAction(String deviceId, Long jobId);

    /**
     * 创建升级任务
     * <AUTHOR>
     * @date 16:47 2022/8/1
     * @param jobAddDto:
     * @return void
     **/
    void add(JobAddDto jobAddDto);

    /**
     * 编辑固件升级任务
     * <AUTHOR>
     * @date 14:52 2022/7/29
     * @param jobEditDto:
     * @return void
     **/
    void edit(JobEditDto jobEditDto);

    /**
     * 删除固件升级任务
     * <AUTHOR>
     * @date 14:57 2022/7/13
     * @param jobId: 任务id
     * @return void
     **/
    void delete(SingleInfoReq<Long> jobId);

    /**
     * 获取上传固件预签名url，有效期30min
     * <AUTHOR>
     * @date 17:30 2022/7/29
     * @param uploadUrlDto:
     * @return com.chervon.technology.domain.vo.ota.PreSignedUrlVo
     **/
    PreSignedUrlVo getUploadUrl(UploadUrlDto uploadUrlDto);

    /**
     * 获取下载url，有效期30min
     * <AUTHOR>
     * @date 16:52 2022/8/1
     * @param key:
     * @return java.lang.String
     **/
    String getDownloadUrl(String key);

    /**
     * 分页查询任务列表
     * <AUTHOR>
     * @date 19:49 2022/8/1
     * @param jobListDto:
     * @return com.chervon.common.core.domain.PageResult<com.chervon.technology.domain.vo.ota.JobListVo>
     **/
    PageResult<JobListVo> listPage(JobListDto jobListDto);

    /**
     * 根据任务状态List查询
     *
     * @return com.chervon.common.core.domain.PageResult<com.chervon.technology.domain.vo.ota.JobListVo>
     * <AUTHOR>
     * @date 2022年9月27日
     **/
    List<JobReleaseListVo> listReleaseByStatusList(Long productId,
        List<OtaJobReleaseStatus> releaseStatusList);

    /**
     * 获取任务详情
     * <AUTHOR>
     * @date 19:50 2022/8/1
     * @param jobId:
     * @return com.chervon.technology.api.vo.ota.JobDetailVo
     **/
    JobDetailVo getDetail(Long jobId);

    /**
     * 更新任务开发状态
     * <AUTHOR>
     * @date 20:13 2022/8/2
     * @param jobStatusDto: 
     * @return void
     **/
    void updateDevelopStatus(JobStatusDto jobStatusDto);

    /**
     * 获取兼容版本列表
     * <AUTHOR>
     * @date 15:18 2022/8/3
     * @param listVersionDto:
     * @return java.util.List<java.lang.String>
     **/
    List<String> listVersions(ListVersionDto listVersionDto);

    /**
     * 根据jobId获取任务文档
     * <AUTHOR>
     * @date 15:58 2022/8/10
     * @param jobId:
     * @return com.chervon.iot.middle.api.pojo.ota.JobDocument
     **/
    JobDocument getJobDocument(Long jobId);

    /**
     * 分页查询任务发布管理列表
     * <AUTHOR>
     * @date 11:57 2022/8/11
     * @param jobReleaseListDto:
     * @return com.chervon.common.core.domain.PageResult<com.chervon.technology.api.vo.ota.JobReleaseListVo>
     **/
    PageResult<JobReleaseListVo> pageRelease(JobReleaseListDto jobReleaseListDto);

    /**
     * 分页查询任务发布管理列表
     * <AUTHOR>
     * @date 11:57 2022/8/11
     * @param jobReleaseListDto:
     * @return com.chervon.common.core.domain.PageResult<com.chervon.technology.api.vo.ota.JobReleaseListVo>
     **/
    List<JobReleaseListVo> listRelease(JobReleaseListDto jobReleaseListDto);

    /**
     * 获取任务发布详情
     * <AUTHOR>
     * @date 11:11 2022/8/12
     * @param jobId: 任务id
     * @return com.chervon.technology.api.vo.ota.JobReleaseDetailVo
     **/
    JobReleaseDetailVo getReleaseDetail(Long jobId);

    /**
     * 更新发布状态
     * <AUTHOR>
     * @date 14:01 2022/8/15
     * @param jobId: 任务id
     * @param status: 任务发布状态
     * @return void
     **/
    void updateReleaseStatus(Long jobId, OtaJobReleaseStatus status);

    /**
     * 更新自定义版本号
     * <AUTHOR>
     * @date 17:33 2022/8/15
     * @param jobId: 任务id
     * @param customVersion: 自定义版本号
     **/
    void updateCustomVersion(Long jobId, String customVersion);

    /**
     * 更新
     * <AUTHOR>
     * @date 17:33 2022/8/15
     * @param jobId:
     * @param technologyVersion:
     * @return void
     **/
    void updateTechnologyVersion(Long jobId, String technologyVersion);

    /**
     * 获取升级信息，无可用升级返回null
     * <AUTHOR>
     * @date 16:35 2022/8/24
     * @param deviceId: 设备id
     * @param singleMcu: 是否为单MCU升级，true 单MCU升级，false 整合升级
     * @param componentMap: 总成零件map，key为总成零件号，value为总成零件版本号
     * @param lang: 语言
     * @param shortToken 短token 可以获取userID
     * @return com.chervon.iot.middle.api.pojo.ota.JobDocument
     **/
    JobDocument check(String deviceId, Boolean singleMcu, Map<String, String> componentMap,
        String lang,String shortToken);

    /**
     * 刷新任务执行状态，每分钟执行一次
     * <AUTHOR>
     * @date 17:47 2022/9/2
     * @return void
     **/
    void refreshJobStatus();

    /**
     * 更新设备执行job状态
     * <AUTHOR>
     * @date 15:15 2022/9/3
     * @param jobStatusRuleDto:
     * @return void
     **/
    void reportJobStatus(JobStatusRuleDto jobStatusRuleDto);

    /**
     * 检查升级更新
     * <AUTHOR>
     * @date 10:03 2022/10/3
     * @param jsonObject:
     * @return void
     **/
    void otaCheck(CheckJobDto jsonObject) throws IOException;

    /**
     * 发布升级任务到job
     * <AUTHOR>
     * @date 10:03 2022/10/3
     * @param jobId:
     * @param productId:
     * @param isTest:
     * @return null
     **/
    String publishJob(Long jobId, Long productId, Boolean isTest);

    /**
     * 获取技术版本号
     * <AUTHOR>
     * @date 15:43 2022/10/3
     * @param productId:
     * @param jobId:
     * @return java.lang.String
     **/
    String getTechnologyVersion(Long productId, Long jobId);

    /**
     * 更新ota结果
     * @param jobId
     * @param deviceId
     * @param otaResultDto
     */
    void updateOtaStatus(Long jobId, String deviceId, OtaResultDto otaResultDto);
}
