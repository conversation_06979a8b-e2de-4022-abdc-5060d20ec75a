package com.chervon.technology.rpc;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.mybatis.util.LoginUserUtil;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.sso.CurrentLoginUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.middle.api.service.RemoteOtaService;
import com.chervon.operation.api.RemoteGroupService;
import com.chervon.technology.api.RemoteOtaJobService;
import com.chervon.technology.api.dto.DeviceJobResultDto;
import com.chervon.technology.api.dto.ota.*;
import com.chervon.technology.api.enums.OtaJobDevelopStatus;
import com.chervon.technology.api.enums.OtaJobReleaseOperation;
import com.chervon.technology.api.enums.OtaJobReleaseStatus;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.ota.*;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.config.IotPlatformCommon;
import com.chervon.technology.config.OtaOperationConfig;
import com.chervon.technology.domain.dataobject.ReleaseRecord;
import com.chervon.technology.domain.dto.ota.DeviceOtaDetailDto;
import com.chervon.technology.domain.entity.OtaDeviceComponentResult;
import com.chervon.technology.domain.entity.OtaDeviceResult;
import com.chervon.technology.domain.entity.OtaJob;
import com.chervon.technology.domain.entity.OtaJobGroup;
import com.chervon.technology.domain.enums.ComponentResultStatusEnum;
import com.chervon.technology.mapper.OtaDeviceComponentResultMapper;
import com.chervon.technology.mapper.OtaDeviceResultMapper;
import com.chervon.technology.service.*;
import com.chervon.technology.service.impl.OtaDeviceComponentResultServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;
import java.util.stream.Collectors;

import static com.chervon.technology.api.exception.TechnologyErrorCode.*;

/**
 * <AUTHOR>
 * @className RemoteOtaService
 * @description
 * @date 2022/8/4 11:08
 */
@Slf4j
@DubboService
public class RemoteOtaJobServiceImpl implements RemoteOtaJobService {
    /**
     * 发布类型 1固件发布
     **/
    private static final Integer RELEASE_TYPE = CommonConstant.ONE;

    @Resource
    private ProductComponentService productComponentService;
    @Resource
    private FirmwareService firmwareService;
    @Resource
    private OtaDeviceComponentResultMapper otaDeviceComponentResultMapper;
    @Resource
    private OtaDeviceResultMapper otaDeviceResultMapper;
    @Resource
    private OtaDeviceResultService otaDeviceResultService;
    @Resource
    private OtaJobService otaJobService;
    @Resource
    private OtaJobGroupService otaJobGroupService;
    @Resource
    private OtaOperationConfig otaOperationConfig;
    @Resource
    private ReleaseRecordService releaseRecordService;
    @Resource
    private OtaDeviceComponentResultServiceImpl otaDeviceComponentResultService;

    @DubboReference
    private RemoteMultiLanguageService languageService;
    @DubboReference
    private RemoteOtaService remoteOtaService;

    @DubboReference
    private RemoteGroupService remoteGroupService;

    @Override
    public List<ComponentResultDto> getDeviceComponentResult(DeviceJobResultDto deviceJobResultDto) {
        DeviceOtaDetailDto deviceOtaDetailDto = new DeviceOtaDetailDto();
        deviceOtaDetailDto.setDeviceId(deviceJobResultDto.getDeviceId());
        deviceOtaDetailDto.setJobId(deviceJobResultDto.getJobId().get(0));
        List<OtaDeviceComponentResult> deviceComponentList = otaDeviceComponentResultService.getDeviceComponentList(deviceOtaDetailDto);
        return deviceComponentList.stream().map(component -> {
            ComponentResultDto componentResultDto = new ComponentResultDto();
            componentResultDto.setPackageId(component.getFirmwareId());
            componentResultDto.setStatus(component.getStatus());
            componentResultDto.setDetail(component.getDetail());
            String key = RedisConstant.OTA_RESULT + deviceJobResultDto.getDeviceId() + "_" + deviceJobResultDto.getJobId().get(0)
                    + "_" + component.getFirmwareId();
            ComponentResultDto cacheComponent = RedisUtils.getCacheObject(key);
            if (cacheComponent != null) {
                componentResultDto.setSysCurrentTime(System.currentTimeMillis() / 1000);
                componentResultDto.setUpgradeStartTime(cacheComponent.getUpgradeStartTime());
                componentResultDto.setUpgradeExpectTime(cacheComponent.getUpgradeExpectTime());
                componentResultDto.setSysReportTime(cacheComponent.getSysReportTime());
            }
            return componentResultDto;
        }).collect(Collectors.toList());
    }

    @Override
    public void confirmDeviceUpgrade(DeviceJobResultDto deviceJobResultDto) {
        Long jobId=deviceJobResultDto.getJobId().get(0);
        LambdaQueryWrapper<OtaDeviceResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaDeviceResult::getDeviceId, deviceJobResultDto.getDeviceId())
                .in(OtaDeviceResult::getJobId, deviceJobResultDto.getJobId());
        List<OtaDeviceResult> listOtaDeviceResult = otaDeviceResultService.list(wrapper);
        if(!CollectionUtils.isEmpty(listOtaDeviceResult)){
            for(OtaDeviceResult otaDeviceResult : listOtaDeviceResult) {
                if (otaDeviceResult != null) {
                    otaDeviceResult.setAppUserId(deviceJobResultDto.getUserId());
                    otaDeviceResultService.saveOrUpdate(otaDeviceResult);
                    log.info("固件升级记录保存成功:{}",deviceJobResultDto);
                }else{
                    log.error("固件升级记录保存失败:{}",deviceJobResultDto);
                }
            }
        }

        LambdaQueryWrapper<OtaDeviceComponentResult> wrapperComponent = new LambdaQueryWrapper<>();
        wrapperComponent.eq(OtaDeviceComponentResult::getDeviceId, deviceJobResultDto.getDeviceId())
                .eq(OtaDeviceComponentResult::getJobId, jobId);
        List<OtaDeviceComponentResult> list = otaDeviceComponentResultService.list(wrapperComponent);
        if (CollectionUtil.isNotEmpty(list)) {
            list.stream().forEach(a -> a.setAppUserId(deviceJobResultDto.getUserId()));
            otaDeviceComponentResultService.saveOrUpdateBatch(list);
        }
    }

    @Override
    public List<OtaHistoryVo> getOtaHistory(String deviceId, Long userId) {
        List<OtaHistoryVo> otaHistoryList = otaDeviceResultMapper.getOtaHistory(deviceId, userId);
        String lang = LocaleContextHolder.getLocale().getLanguage();
        lang = StringUtils.isNotEmpty(lang) ? lang : "en";
        if (CollectionUtil.isNotEmpty(otaHistoryList)) {
            for (OtaHistoryVo otaHistoryVo : otaHistoryList) {
                if (StringUtils.isNotEmpty(otaHistoryVo.getContent())) {
                    String content = com.chervon.technology.config.MultiLanguageUtil.getByLangCode(otaHistoryVo.getContent(), lang);
                    otaHistoryVo.setContent(content);
                }
            }
        }
        return otaHistoryList;
    }

    @Override
    public PageResult<JobConfigListVo> pageConfig(JobConfigListDto jobConfigListDto) {
        Page<OtaJob> pageRequest = new Page<>(jobConfigListDto.getPageNum(),
                jobConfigListDto.getPageSize());
        LambdaQueryWrapper<OtaJob> queryWrapper = new LambdaQueryWrapper<>();
        if (jobConfigListDto.getProductId() != null) {
            queryWrapper.eq(OtaJob::getProductId, jobConfigListDto.getProductId());
        }
        if (jobConfigListDto.getJobId() != null) {
            queryWrapper.apply(OtaJob.ID + " like {0}",
                    "%" + jobConfigListDto.getJobId() + "%");
        }
        if (jobConfigListDto.getReleaseStatus() != null) {
            queryWrapper.eq(OtaJob::getReleaseStatus, jobConfigListDto.getReleaseStatus());
        }
        queryWrapper.eq(null != jobConfigListDto.getDevelopStatus(),
                OtaJob::getDevelopStatus, jobConfigListDto.getDevelopStatus());
        queryWrapper.ge(StringUtils.isNotBlank(jobConfigListDto.getCreateStartTime()),
                OtaJob::getCreateTime, jobConfigListDto.getCreateStartTime());
        queryWrapper.le(StringUtils.isNotBlank(jobConfigListDto.getCreateEndTime()),
                OtaJob::getCreateTime, jobConfigListDto.getCreateEndTime());
        queryWrapper.ge(StringUtils.isNotBlank(jobConfigListDto.getUpdateStartTime()),
                OtaJob::getUpdateTime, jobConfigListDto.getUpdateStartTime());
        queryWrapper.le(StringUtils.isNotBlank(jobConfigListDto.getUpdateEndTime()),
                OtaJob::getUpdateTime, jobConfigListDto.getUpdateEndTime());
        // 运营平台只能看到封板状态的任务
        queryWrapper.orderByDesc(OtaJob::getCreateTime);
        Page<OtaJob> page = otaJobService.page(pageRequest, queryWrapper);
        List<JobConfigListVo> jobConfigListVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            for (OtaJob otaJob : page.getRecords()) {
                JobConfigListVo jobConfigListVo = ConvertUtil.convert(otaJob, JobConfigListVo.class);
                jobConfigListVo.setCanViewJobDetail(
                        !OtaOperationConfig.canViewJobDetailSet.contains(otaJob.getReleaseStatus()));
                jobConfigListVo.setCanApplyRelease(
                        otaOperationConfig.hasApplyReleasePermission(otaJob));
                jobConfigListVo.setCanCancelApplyRelease(
                        otaOperationConfig.hasCancelApplyReleasePermission(otaJob.getReleaseStatus())
                );
                jobConfigListVo.setCanApplyStopRelease(
                        otaOperationConfig.hasApplyStopReleasePermission(otaJob.getReleaseStatus())
                );
                jobConfigListVo.setCanCancelStopRelease(
                        otaOperationConfig.hasCancelStopReleasePermission(otaJob.getReleaseStatus())
                );
                jobConfigListVo.setCanViewTestRefuseReason(
                        OtaJobReleaseStatus.RELEASE_TEST_REFUSED.equals(otaJob.getReleaseStatus())
                );
                jobConfigListVo.setCanViewApplyRefuseReason(
                        OtaJobReleaseStatus.RELEASE_REFUSED.equals(otaJob.getReleaseStatus())
                );
                jobConfigListVo.setCanViewStopRefuseReason(
                        OtaJobReleaseStatus.STOP_REFUSED.equals(otaJob.getReleaseStatus())
                );
                jobConfigListVo.setCanConfigRelease(
                        OtaJobReleaseStatus.RELEASE_WAITING.equals(otaJob.getReleaseStatus()) ||
                                OtaJobReleaseStatus.RELEASE_REFUSED.equals(otaJob.getReleaseStatus()) ||
                                OtaJobReleaseStatus.RELEASE_TEST_REFUSED.equals(otaJob.getReleaseStatus())
                );
                jobConfigListVo.setCanViewReleaseDetail(OtaOperationConfig.viewReleaseDetailList.contains(otaJob.getReleaseStatus()));
                jobConfigListVo.setCanViewOtaResult(OtaOperationConfig.viewOtaResultList.contains(otaJob.getReleaseStatus()));

                jobConfigListVo.setCanNullifyApply(OtaJobReleaseStatus.OVER.equals(otaJob.getReleaseStatus()) ||
                        OtaJobReleaseStatus.STOPPED.equals(otaJob.getReleaseStatus()) ||
                        OtaJobReleaseStatus.NULLIFY_REJECTED.equals(otaJob.getReleaseStatus()));
                jobConfigListVo.setCanNullifyApplyCancel(OtaJobReleaseStatus.NULLIFY_IN_REVIEW.equals(otaJob.getReleaseStatus()));
                jobConfigListVo.setCanViewNullifyRefusedReason(OtaJobReleaseStatus.NULLIFY_REJECTED.equals(otaJob.getReleaseStatus()));
                jobConfigListVos.add(jobConfigListVo);
            }
        }
        PageResult pageResult = new PageResult(page.getCurrent(), page.getSize(), page.getTotal());
        pageResult.setList(jobConfigListVos);
        return pageResult;
    }

    @Override
    public PageResult<JobReleaseListVo> pageRelease(JobReleaseListDto jobReleaseListDto) {
        PageResult<JobReleaseListVo> pageResult = otaJobService.pageRelease(jobReleaseListDto);
        return pageResult;
    }

    @Override
    public List<JobReleaseListVo> listRelease(JobReleaseListDto jobReleaseListDto) {
        return otaJobService.listRelease(jobReleaseListDto);
    }

    @Override
    public JobDetailVo getDetail(Long jobId, String lang) {
        LocaleContextHolder.setLocale(new Locale(lang));
        return otaJobService.getDetail(jobId);
    }

    @Override
    public JobReleaseDetailVo getReleaseDetail(Long jobId) {
        return otaJobService.getReleaseDetail(jobId);
    }

    private void checkConfig(Integer startType, String startZone, Date startTime, Integer endType, String endZone, Date endTime) {
        if (endType == 1) {
            // 结束时间是永久
            return;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String endTimeS = sdf.format(endTime);
        LocalDateTime endL = LocalDateTime.parse(endTimeS, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        ZonedDateTime endZ = ZonedDateTime.of(endL, ZoneId.of(endZone));
        ZonedDateTime startZ;
        if (startType == 1) {
            startZ = ZonedDateTime.now();
        } else {
            String startTimeS = sdf.format(startTime);
            LocalDateTime startL = LocalDateTime.parse(startTimeS, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            startZ = ZonedDateTime.of(startL, ZoneId.of(startZone));
        }
        if (!endZ.isAfter(startZ)) {
            throw ExceptionMessageUtil.getException(JOB_CONFIG_END_BEFORE_START);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void configJobRelease(JobConfigDto jobConfigDto, String lang) {
        if (jobConfigDto.getStartType() == null || !Arrays.asList(1, 2).contains(jobConfigDto.getStartType())) {
            throw ExceptionMessageUtil.getException(JOB_CONFIG_START_ERROR);
        } else if (jobConfigDto.getStartType() == 2 && (StringUtils.isBlank(jobConfigDto.getStartZone()) || jobConfigDto.getStartTime() == null)) {
            throw ExceptionMessageUtil.getException(JOB_CONFIG_START_ERROR);
        }
        if (jobConfigDto.getEndType() == null || !Arrays.asList(1, 2).contains(jobConfigDto.getEndType())) {
            throw ExceptionMessageUtil.getException(JOB_CONFIG_END_ERROR);
        } else if (jobConfigDto.getEndType() == 2 && (StringUtils.isBlank(jobConfigDto.getEndZone()) || jobConfigDto.getEndTime() == null)) {
            throw ExceptionMessageUtil.getException(JOB_CONFIG_END_ERROR);
        }
        checkConfig(jobConfigDto.getStartType(), jobConfigDto.getStartZone(), jobConfigDto.getStartTime(), jobConfigDto.getEndType(), jobConfigDto.getEndZone(), jobConfigDto.getEndTime());
        if (CollectionUtils.isEmpty(jobConfigDto.getTestGroupNames())) {
            throw ExceptionMessageUtil.getException(JOB_CONFIG_TEST_GROUP_EMPTY);
        }
        Map<String, Long> m = remoteGroupService.countByNames(jobConfigDto.getTestGroupNames());
        m.forEach((k, v) -> {
            if (v <= 0) {
                throw ExceptionMessageUtil.getException(JOB_TEST_GROUP_NOT_EXIST, k);
            }
        });
        if (!CollectionUtils.isEmpty(jobConfigDto.getProductGroupNames())) {
            Map<String, Long> mm = remoteGroupService.countByNames(jobConfigDto.getProductGroupNames());
            mm.forEach((k, v) -> {
                if (v <= 0) {
                    throw ExceptionMessageUtil.getException(JOB_PRD_GROUP_NOT_EXIST, k);
                }
            });
        }

        OtaJob otaJob = ConvertUtil.convert(jobConfigDto, OtaJob.class);
        otaJob.setId(jobConfigDto.getJobId());
        if (otaJob.getStartTime() == null) {
            //起始时间为null 表示当前时间立刻开始
            otaJob.setStartTime(new Date());
        }
        MultiLanguageBo multiLanguageBo = languageService.simpleCreateMultiLanguage(
                IotPlatformCommon.APPLICATION_NAME,
                jobConfigDto.getReleaseContent(),
                lang);
        otaJob.setReleaseContent(multiLanguageBo.getLangId().toString());
        otaJobService.updateById(otaJob);
        List<OtaJobGroup> otaJobGroups = new ArrayList<>();
        List<String> testGroupNames = jobConfigDto.getTestGroupNames();
        if (CollectionUtil.isNotEmpty(testGroupNames)) {
            for (String groupName : testGroupNames) {
                OtaJobGroup otaJobGroup = new OtaJobGroup();
                otaJobGroup.setGroupType(CommonConstant.ZERO);
                otaJobGroup.setGroupName(groupName);
                otaJobGroup.setJobId(jobConfigDto.getJobId());
                otaJobGroups.add(otaJobGroup);
            }
        }
        List<String> productGroupNames = jobConfigDto.getProductGroupNames();
        if (CollectionUtil.isNotEmpty(productGroupNames)) {
            for (String groupName : productGroupNames) {
                OtaJobGroup otaJobGroup = new OtaJobGroup();
                otaJobGroup.setGroupType(CommonConstant.ONE);
                otaJobGroup.setGroupName(groupName);
                otaJobGroup.setJobId(jobConfigDto.getJobId());
                otaJobGroups.add(otaJobGroup);
            }
        }
        if (CollectionUtil.isNotEmpty(otaJobGroups)) {
            otaJobGroupService.removeByJobId(jobConfigDto.getJobId());
            otaJobGroupService.saveBatch(otaJobGroups);
        }
    }


    /**
     * 获取客户版本号
     *
     * @param productId 产品id
     * @return 客户版本号
     */
    public String getCustomVersion(Long productId, LocalDateTime dateTime) {
        List<String> components = productComponentService.listComponentNosByPid(productId);
        // 单MCU产品（R设备）暂不给客户版本号填值
        if (components.size() == 1) {
            return null;
        }
        return dateTime.atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneOffset.ofHours(8))
                .format(new DateTimeFormatterBuilder().appendPattern("yyMMddHH").toFormatter());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(JobReleaseStatusDto jobReleaseStatusDto) {
        Long jobId = jobReleaseStatusDto.getJobId();
        OtaJob otaJob = otaJobService.getById(jobId);
        if (otaJob == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.JOB_NOT_EXISTED);
        }
        OtaJobReleaseStatus targetStatus = null;
        switch (jobReleaseStatusDto.getOperation()) {
            case PASSED: {
                if (otaJob.getStartTime() == null ||
                        otaJob.getStartTime().getTime() <= System.currentTimeMillis()) {
                    targetStatus = OtaJobReleaseStatus.RELEASED;
                    // 发布到正式分组
                    String customVersion = otaJobService.publishJob(jobId, otaJob.getProductId(), false);
                    otaJob.setCustomVersion(customVersion);
                } else {
                    String customVersion = getCustomVersion(otaJob.getProductId(), LocalDateTime.now());
                    otaJob.setCustomVersion(customVersion);
                    targetStatus = OtaJobReleaseStatus.RELEASE_READY;
                }
                otaJob.setEnsuredBy(LoginUserUtil.getSign());
                otaJob.setEnsuredTime(LocalDateTime.now());
                break;
            }
            case STOP_PASSED: {
                remoteOtaService.cancelOtaJob(jobId.toString());
                targetStatus = OtaJobReleaseStatus.STOPPED;
                break;
            }
            case APPLY: {
                if (!OtaJobDevelopStatus.CLOSED.equals(otaJob.getDevelopStatus())) {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_STATUE_NO_CLOSED);
                }
                otaJob.setApplyBy(LoginUserUtil.getSign());
                otaJob.setApplyTime(LocalDateTime.now());
                // 申请发布，更新技术版本号
                String technologyVersion = otaJobService.getTechnologyVersion(
                        otaJob.getProductId(), jobId);
                otaJobService.updateTechnologyVersion(jobId, technologyVersion);
                targetStatus = OtaJobReleaseStatus.TESTING;
                // 发布到测试分组
                otaJobService.publishJob(jobId, otaJob.getProductId(), true);
                break;
            }
            case APPLY_CANCEL: {
                targetStatus = OtaJobReleaseStatus.RELEASE_WAITING;
                break;
            }
            case TEST_PASSED: {
                targetStatus = OtaJobReleaseStatus.RELEASING;
                break;
            }
            case TEST_REFUSED: {
                targetStatus = OtaJobReleaseStatus.RELEASE_TEST_REFUSED;
                break;
            }
            case REFUSED: {
                targetStatus = OtaJobReleaseStatus.RELEASE_REFUSED;
                break;
            }
            case STOP_APPLY: {
                targetStatus = OtaJobReleaseStatus.STOPPING;
                break;
            }
            case STOP_CANCEL: {
                targetStatus = OtaJobReleaseStatus.RELEASED;
                break;
            }
            case STOP_REFUSED: {
                targetStatus = OtaJobReleaseStatus.STOP_REFUSED;
                break;
            }
            case NULLIFY_APPLY: {
                otaJob.setApplyBy(LoginUserUtil.getSign());
                otaJob.setApplyTime(LocalDateTime.now());
                targetStatus = OtaJobReleaseStatus.NULLIFY_IN_REVIEW;
                break;
            }
            case NULLIFY_APPLY_CANCEL: {
                OtaJobReleaseStatus nullifyApplyStatus = getNullifyApplyStatus(jobId);
                targetStatus = nullifyApplyStatus;
                break;
            }
            case NULLIFY_REFUSED: {
                otaJob.setEnsuredBy(LoginUserUtil.getSign());
                otaJob.setEnsuredTime(LocalDateTime.now());
                targetStatus = OtaJobReleaseStatus.NULLIFY_REJECTED;
                break;
            }
            case NULLIFY_PASSED: {
                otaJob.setEnsuredBy(LoginUserUtil.getSign());
                otaJob.setEnsuredTime(LocalDateTime.now());
                targetStatus = OtaJobReleaseStatus.NULLIFIED;
                break;
            }
            default:
                break;
        }
        OtaJobReleaseStatus sourceStatus = otaJob.getReleaseStatus();
        otaJob.setReleaseStatus(targetStatus);
        otaJob.setUpdateBy(null);
        otaJobService.updateById(otaJob);
        saveRecord(jobId, sourceStatus, targetStatus, jobReleaseStatusDto.getOperation(),
                jobReleaseStatusDto.getDescription());
    }

    @Override
    public OtaResultVo getOtaResult(OtaResultDto otaResultDto) {
        Page<OtaResultItemVo> page = new Page<>(otaResultDto.getPageNum(),
                otaResultDto.getPageSize());
        if (StringUtils.isNotEmpty(otaResultDto.getStatus())) {
            otaResultDto.setStatusInt(ComponentResultStatusEnum.valueOf(otaResultDto.getStatus()).getStatus());
        }
        Page<OtaResultItemVo> result = otaDeviceResultMapper.pageOtaResult(page, otaResultDto);
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            List<String> deviceIds = result.getRecords().stream().map(OtaResultItemVo::getDeviceId).collect(Collectors.toList());
            List<ComponentResultVo> componentResults = otaDeviceResultMapper.getComponentResults(deviceIds, otaResultDto);
            Map<String, List<ComponentResultVo>> componentResultMap = componentResults.stream().collect(Collectors.groupingBy(ComponentResultVo::getDeviceId));
            result.getRecords().forEach(otaResultItemVo -> {
                List<ComponentResultVo> componentResultVos = componentResultMap.get(otaResultItemVo.getDeviceId());
                otaResultItemVo.setComponentResults(componentResultVos);
            });
        }
        OtaResultVo otaResultVo = new OtaResultVo();
        PageResult<OtaResultItemVo> pageResult = new PageResult<>(result.getCurrent(),
                result.getSize(), result.getTotal());
        pageResult.setPages(result.getPages());
        pageResult.setList(result.getRecords());
        otaResultVo.setPageResult(pageResult);
        otaResultVo.setTotal(result.getTotal());
        Long succeedCount = otaDeviceResultService.countSucceed(otaResultDto.getJobId());
        Long failedCount = otaDeviceResultService.countFailed(otaResultDto.getJobId());
        otaResultVo.setSucceed(succeedCount);
        otaResultVo.setFailed(failedCount);
        // 设备表 job表
        return otaResultVo;
    }

    @Override
    public List<String> getGroups(Long jobId) {
        LambdaQueryWrapper<OtaJobGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OtaJobGroup::getGroupName).eq(OtaJobGroup::getJobId, jobId);
        List<Object> objects = otaJobGroupService.listObjs(wrapper);
        return (List<String>) (List) objects;
    }

    @Override
    public String getRefuseReason(Long jobId, OtaJobReleaseOperation status) {
        return releaseRecordService.getRefuseReason(jobId, status);
    }

    @Override
    public List<Long> getJobIdsByGroupName(String groupName) {
        return otaJobGroupService.getJobIdsByGroupName(groupName);
    }

    /**
     * 保存任务审批记录
     *
     * @param jobId:        任务id
     * @param sourceStatus: 任务原状态
     * @param targetStatus: 任务目标状态
     * @param operation:    任务审批操作
     * @return void
     * <AUTHOR>
     * @date 13:47 2022/8/15
     **/
    private void saveRecord(Long jobId, OtaJobReleaseStatus sourceStatus,
                            OtaJobReleaseStatus targetStatus,
                            OtaJobReleaseOperation operation, String description) {
        ReleaseRecord record = new ReleaseRecord();
        record.setReleaseId(jobId);
        record.setSourceStatus(sourceStatus == null ? null : sourceStatus.getValue());
        record.setTargetStatus(targetStatus.getValue());
        record.setType(RELEASE_TYPE);
        record.setReleaseOperationType(operation.getValue());
        record.setReleaseUserId(CurrentLoginUtil.getCurrentId());
        record.setDescription(description);
        releaseRecordService.save(record);
    }


    /**
     * 获取申请作废之前的状态
     *
     * @param jobId: 任务id
     * @return void
     * <AUTHOR>
     * @date 2023年3月15日
     **/
    private OtaJobReleaseStatus getNullifyApplyStatus(Long jobId) {
        LambdaQueryWrapper<ReleaseRecord> wrapper = new LambdaQueryWrapper<ReleaseRecord>().eq(ReleaseRecord::getReleaseId, jobId)
                .eq(ReleaseRecord::getReleaseOperationType, OtaJobReleaseOperation.NULLIFY_APPLY.getValue())
                .select(ReleaseRecord::getSourceStatus)
                .orderByDesc(ReleaseRecord::getCreateTime)
                .last("limit 1");
        ReleaseRecord record = releaseRecordService.getOne(wrapper);
        if (record != null && StringUtils.isNotEmpty(record.getSourceStatus())) {
            return OtaJobReleaseStatus.valueOf(record.getSourceStatus());
        }
        return OtaJobReleaseStatus.STOPPED;
    }
}
