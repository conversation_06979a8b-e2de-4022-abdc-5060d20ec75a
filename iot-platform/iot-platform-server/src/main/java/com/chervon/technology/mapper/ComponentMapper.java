package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.technology.domain.dataobject.Component;
import com.chervon.technology.domain.dto.component.CheckComponentDto;
import com.chervon.technology.domain.vo.component.ComponentCheckVo;
import com.chervon.technology.domain.vo.component.ComponentVo;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 总成零件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
public interface ComponentMapper extends BaseMapper<Component> {

    /**
     * 根据pid获取产品关联的总成零件信息
     * @param pid
     * @return
     */
    List<ComponentVo> getComponentVoByPid(@Param("pid") Long pid);

    /**
     * 根据总成零件号获取关联信息
     * @param componentNo 总成零件号
     * @return 总成关联的产品Id
     */
    List<Long> getListByComponentNo(@Param("componentNo") String componentNo);


    /**
     * 校验总成零件号，如存在返回总成零件详情，不存在返回null
     * <AUTHOR>
     * @date 16:31 2022/8/3
     * @param checkComponentDto:
     * @return com.chervon.technology.domain.vo.component.ComponentCheckVo
     **/
    ComponentCheckVo check(@Param("checkComponentDto") CheckComponentDto checkComponentDto);
}
