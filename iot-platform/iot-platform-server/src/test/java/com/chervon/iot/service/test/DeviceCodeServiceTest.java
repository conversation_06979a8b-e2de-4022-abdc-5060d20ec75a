package com.chervon.iot.service.test;

import com.chervon.technology.IotPlatformApplication;
import com.chervon.technology.api.dto.SearchDeviceCodeDto;
import com.chervon.technology.service.DeviceCodeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

@SpringBootTest(classes = IotPlatformApplication.class)
public class DeviceCodeServiceTest {

    @Autowired
    private DeviceCodeService deviceCodeService;

    @Test
    public void testGetListDevice(){
        SearchDeviceCodeDto searchDeviceCodeDto = new SearchDeviceCodeDto();
        searchDeviceCodeDto.setPageNum(1);
        searchDeviceCodeDto.setPageSize(1);
        searchDeviceCodeDto.setProductSnCode("ST26");
        searchDeviceCodeDto.setIccid("123458");
        assertNotNull(deviceCodeService.getListDevice(searchDeviceCodeDto));
    }
}
