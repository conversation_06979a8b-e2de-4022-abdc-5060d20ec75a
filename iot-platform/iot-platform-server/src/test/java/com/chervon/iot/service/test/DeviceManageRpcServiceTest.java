package com.chervon.iot.service.test;

import com.chervon.technology.IotPlatformApplication;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.vo.DeviceListInfoRpcVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes = IotPlatformApplication.class)
public class DeviceManageRpcServiceTest {

    @Autowired
    RemoteDeviceManageService deviceManageRpcSerivce;

    @Test
    public void test() {
        List<String> deviceIds = new ArrayList<>();
        deviceIds.add("TLM28251119645X");
        List<DeviceListInfoRpcVo> deviceRpcVos = deviceManageRpcSerivce.listInfoByDeviceId(deviceIds);
        assertNotNull(deviceRpcVos);
        System.out.println(deviceRpcVos);
    }
}
