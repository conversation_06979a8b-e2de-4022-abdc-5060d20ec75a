<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chervon.iot</groupId>
    <artifactId>iot-platform-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>iot-platform</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <chervon.version>1.1.0-SNAPSHOT</chervon.version>
        <spring-boot.version>2.6.8</spring-boot.version>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <hutool.version>5.8.2</hutool.version>

        <excel-util.version>1.2.1</excel-util.version>
        <iot-middle-platform-api.version>1.0.1-SNAPSHOT</iot-middle-platform-api.version>
        <iot-app-api.version>1.0.0-SNAPSHOT</iot-app-api.version>
        <user-center-api.version>1.0.3-SNAPSHOT</user-center-api.version>
        <configuration-center-sdk-language.version>1.0.0-SNAPSHOT</configuration-center-sdk-language.version>
        <configuration-center-api.version>1.0.0-SNAPSHOT</configuration-center-api.version>
        <operation-platform-api.version>1.0.1-SNAPSHOT</operation-platform-api.version>
        <message-center-api.version>1.0.3-SNAPSHOT</message-center-api.version>
        <logback-core.version>1.2.11</logback-core.version>
        <logstash-logback-encoder.version>4.10</logstash-logback-encoder.version>
        <xxl-job-core.version>2.3.1</xxl-job-core.version>
        <authority-platform.version>1.0.0-SNAPSHOT</authority-platform.version>
        <maven.deploy.skip>false</maven.deploy.skip>
        <fleet-web-api.version>1.0.0-SNAPSHOT</fleet-web-api.version>
        <software-amazon-awssdk.version>2.17.89</software-amazon-awssdk.version>
        <httpclient5.version>5.1</httpclient5.version>
        <spring.profiles.active>dev</spring.profiles.active>
        <jacoco.version>0.8.7</jacoco.version>
    </properties>

    <modules>
        <module>iot-platform-api</module>
        <module>iot-platform-server</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback-core.version}</version>
            </dependency>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- alibaba cloud 的依赖配置-->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-alibaba-bom</artifactId>
                <version>${chervon.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-bom</artifactId>
                <version>${chervon.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--Project modules-->
            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-platform-api</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-middle-platform-api</artifactId>
                <version>${iot-middle-platform-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-app-api</artifactId>
                <version>${iot-app-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>user-center-api</artifactId>
                <version>${user-center-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>message-center-api</artifactId>
                <version>${message-center-api.version}</version>
            </dependency>
            <!--Project modules End-->

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>configuration-center-sdk-language</artifactId>
                <version>${configuration-center-sdk-language.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>configuration-center-api</artifactId>
                <version>${configuration-center-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>operation-platform-api</artifactId>
                <version>${operation-platform-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>authority-platform-sdk</artifactId>
                <version>${authority-platform.version}</version>
            </dependency>

            <!-- Excel 导入-->
            <dependency>
                <groupId>com.sargeraswang.util</groupId>
                <artifactId>excel-util</artifactId>
                <version>${excel-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>fleet-web-api</artifactId>
                <version>${fleet-web-api.version}</version>
            </dependency>
           <!-- 地图服务依赖 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>auth</artifactId>
                <version>${software-amazon-awssdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>http-client-spi</artifactId>
                <version>${software-amazon-awssdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>regions</artifactId>
                <version>${software-amazon-awssdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>User Porject Release</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>User Porject Snapshot</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>${maven.deploy.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.6.2</version>
                </plugin>

                <!-- JaCoCo插件（代码覆盖率） -->
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco.version}</version>
                    <configuration>
                        <excludes>
                            <!-- 启动类 -->
                            <exclude>**/*Application.class</exclude>
                            <!-- 配置类 -->
                            <exclude>**/*Config.class</exclude>
                            <exclude>**/*Configuration.class</exclude>
                            <!-- 常量类 -->
                            <exclude>**/*Constant*.class</exclude>
                            <exclude>**/constant/**</exclude>
                            <!-- 异常类 -->
                            <exclude>**/*Exception.class</exclude>
                            <exclude>**/exception/**</exclude>
                            <!-- 数据传输对象 -->
                            <exclude>**/*DTO.class</exclude>
                            <exclude>**/*VO.class</exclude>
                            <exclude>**/*Entity.class</exclude>
                            <exclude>**/domain/**</exclude>
                            <!-- 枚举类 -->
                            <exclude>**/enums/**</exclude>
                            <!-- 属性配置类 -->
                            <exclude>**/*Properties.class</exclude>
                            <exclude>**/prop/**</exclude>
                        </excludes>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report-aggregate</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>report-aggregate</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Surefire插件（单元测试运行器） -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0-M7</version>
                    <configuration>
                        <includes>
                            <include>**/*Test.java</include>
                            <include>**/*Tests.java</include>
                        </includes>
                        <excludes>
                            <!-- 排除集成测试类 -->
                            <exclude>**/*IntegrationTest.java</exclude>
                            <exclude>**/*IT.java</exclude>
                            <exclude>**/*IntTest.java</exclude>
                        </excludes>
                        <argLine>@{argLine} -Dfile.encoding=UTF-8</argLine>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- JaCoCo插件（代码覆盖率） -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven-releases</id>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>maven-snapshots</id>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-snapshots/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

</project>
