<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chervon</groupId>
        <artifactId>message-center-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>message-center-server</artifactId>
    <name>message-center-server</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <sk.version>8.15.0</sk.version>
        <iot-middle-platform-api.version>1.0.1-SNAPSHOT</iot-middle-platform-api.version>
        <aws-sdk-ses.version>1.12.215</aws-sdk-ses.version>
    </properties>

    <dependencies>
        <!-- SES -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-ses</artifactId>
            <version>${aws-sdk-ses.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>${sk.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/ch.qos.logback/logback-core -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>message-center-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jdbc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- Chervon Common Log -->
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>iot-middle-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>operation-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>iot-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>configuration-center-sdk-language</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.chervon</groupId>-->
<!--            <artifactId>chervon-common-mongodb</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.gcm</groupId>
            <artifactId>gcm-server</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 苹果推送依赖json-simple包 -->
        <!-- https://mvnrepository.com/artifact/com.turo/pushy -->
        <dependency>
            <groupId>com.turo</groupId>
            <artifactId>pushy</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.google.firebase</groupId>
            <artifactId>firebase-admin</artifactId>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>connect</artifactId>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>pinpoint</artifactId>
        </dependency>



        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
        </dependency>

        <!-- Sa-Token 整合redis (使用jackson序列化方式) -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-dao-redis-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
