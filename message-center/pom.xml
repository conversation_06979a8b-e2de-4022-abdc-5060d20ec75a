<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chervon</groupId>
    <artifactId>message-center-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>message-center</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <chervon.version>1.1.0-SNAPSHOT</chervon.version>
        <spring-boot.version>2.6.8</spring-boot.version>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <hutool.version>5.8.2</hutool.version>
        <iot-middle-platform-api.version>1.0.0-SNAPSHOT</iot-middle-platform-api.version>
        <configuration-center-sdk-language.version>1.0.0-SNAPSHOT</configuration-center-sdk-language.version>
        <gcm-server-version>1.0.0</gcm-server-version>
        <pushy.version>0.13.10</pushy.version>
        <gcm-server.version>1.7.6</gcm-server.version>
        <firebase-admin.version>7.0.0</firebase-admin.version>
        <logback-core.version>1.2.11</logback-core.version>
        <xxl-job-core.version>2.3.1</xxl-job-core.version>
        <logstash-logback-encoder.version>4.10</logstash-logback-encoder.version>
        <operation-platform.version>1.0.0-SNAPSHOT</operation-platform.version>
        <iot-platform-api.version>1.0.2-SNAPSHOT</iot-platform-api.version>
        <pinpoint.version>2.19.4</pinpoint.version>
        <spring-cloud-starter-stream-rocketmq.version>2.2.9.RELEASE</spring-cloud-starter-stream-rocketmq.version>
        <maven.deploy.skip>false</maven.deploy.skip>
        <satoken.version>1.34.0</satoken.version>
        <spring.profiles.active>dev</spring.profiles.active>
        <jacoco.version>0.8.7</jacoco.version>
    </properties>

    <modules>
        <module>message-center-api</module>
        <module>message-center-server</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>operation-platform-api</artifactId>
                <version>${operation-platform.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-platform-api</artifactId>
                <version>${iot-platform-api.version}</version>
            </dependency>
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback-core.version}</version>
            </dependency>
            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- alibaba cloud 的依赖配置-->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-alibaba-bom</artifactId>
                <version>${chervon.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-bom</artifactId>
                <version>${chervon.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--Project modules-->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>message-center-api</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>
            <!--Project modules End-->

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>configuration-center-sdk-language</artifactId>
                <version>${configuration-center-sdk-language.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-middle-platform-api</artifactId>
                <version>${iot-middle-platform-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.gcm</groupId>
                <artifactId>gcm-server</artifactId>
                <version>${gcm-server-version}</version>
                <optional>true</optional>
            </dependency>
            <!-- 苹果推送依赖json-simple包 -->
            <!-- https://mvnrepository.com/artifact/com.turo/pushy -->
            <dependency>
                <groupId>com.turo</groupId>
                <artifactId>pushy</artifactId>
                <version>${pushy.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>${gcm-server.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>${firebase-admin.version}</version>
            </dependency>

            <!-- AWS Pinpoint SDK -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>pinpoint</artifactId>
                <version>${pinpoint.version}</version>
            </dependency>
            <!-- AWS java SDK -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>connect</artifactId>
                <version>2.25.41</version>
            </dependency>

            <!-- RocketMQ -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
                <version>${spring-cloud-starter-stream-rocketmq.version}</version>
            </dependency>

            <!-- Sa-Token 整合redis (使用jackson序列化方式) -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-dao-redis-jackson</artifactId>
                <version>${satoken.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>User Porject Release</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>User Porject Snapshot</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>maven-releases</id>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>maven-snapshots</id>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-snapshots/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>${maven.deploy.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.6.2</version>
                </plugin>

                <!-- JaCoCo插件（代码覆盖率） -->
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco.version}</version>
                    <configuration>
                        <excludes>
                            <!-- 启动类 -->
                            <exclude>**/*Application.class</exclude>
                            <!-- 配置类 -->
                            <exclude>**/*Config.class</exclude>
                            <exclude>**/*Configuration.class</exclude>
                            <!-- 常量类 -->
                            <exclude>**/*Constant*.class</exclude>
                            <exclude>**/constant/**</exclude>
                            <!-- 异常类 -->
                            <exclude>**/*Exception.class</exclude>
                            <exclude>**/exception/**</exclude>
                            <!-- 数据传输对象 -->
                            <exclude>**/*DTO.class</exclude>
                            <exclude>**/*VO.class</exclude>
                            <exclude>**/*Entity.class</exclude>
                            <exclude>**/domain/**</exclude>
                            <!-- 枚举类 -->
                            <exclude>**/enums/**</exclude>
                            <!-- 属性配置类 -->
                            <exclude>**/*Properties.class</exclude>
                            <exclude>**/prop/**</exclude>
                        </excludes>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report-aggregate</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>report-aggregate</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Surefire插件（单元测试运行器） -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0-M7</version>
                    <configuration>
                        <includes>
                            <include>**/*Test.java</include>
                            <include>**/*Tests.java</include>
                        </includes>
                        <excludes>
                            <!-- 排除集成测试类 -->
                            <exclude>**/*IntegrationTest.java</exclude>
                            <exclude>**/*IT.java</exclude>
                            <exclude>**/*IntTest.java</exclude>
                        </excludes>
                        <argLine>@{argLine} -Dfile.encoding=UTF-8</argLine>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- JaCoCo插件（代码覆盖率） -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
