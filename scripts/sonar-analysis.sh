#!/bin/bash

echo "===========================================" 
echo "开始 chervon-common 代码质量分析..."
echo "==========================================="

# 设置环境变量
BRANCH_NAME=${1:-"feature_unitTest"}
PROJECT_VERSION=${2:-"1.1"}

echo "分支: $BRANCH_NAME"
echo "版本: $PROJECT_VERSION"
echo "-------------------------------------------"

# 执行Maven测试和SonarQube分析
mvn clean verify org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar \
  -Dsonar.branch.name=$BRANCH_NAME \
  -Dsonar.ws.timeout=300 \
  -Dsonar.projectKey=chervon-common \
  -Dsonar.projectName="Chervon Common" \
  -Dsonar.projectVersion=$PROJECT_VERSION \
  -Dsonar.host.url=http://************** \
  -Dsonar.login=**************************************** \
  -Dsonar.java.coveragePlugin=jacoco \
  -Dsonar.java.binaries=target/classes \
  -Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "==========================================="
    echo "✅ 分析完成！"
    echo "📊 查看SonarQube报告: http://**************/dashboard?id=chervon-common&branch=$BRANCH_NAME"
    echo "📋 查看JaCoCo报告: target/site/jacoco-aggregate/index.html"
    echo "==========================================="
else
    echo "==========================================="
    echo "❌ 分析失败，请检查错误信息"
    echo "==========================================="
    exit 1
fi
