#!/bin/bash

echo "==========================================="
echo "开始运行单元测试和生成覆盖率报告..."
echo "==========================================="

# 运行测试并生成JaCoCo报告
mvn clean verify

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "==========================================="
    echo "✅ 测试完成！"
    echo "📋 查看覆盖率报告:"
    echo "   - 聚合报告: target/site/jacoco-aggregate/index.html"
    echo "   - 各模块报告: [模块名]/target/site/jacoco/index.html"
    echo "==========================================="
    
    # 尝试自动打开聚合报告（macOS）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if [ -f "target/site/jacoco-aggregate/index.html" ]; then
            echo "🚀 正在打开覆盖率报告..."
            open target/site/jacoco-aggregate/index.html
        fi
    fi
else
    echo "==========================================="
    echo "❌ 测试失败，请检查错误信息"
    echo "==========================================="
    exit 1
fi
