package com.chervon.common.core.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

import java.security.NoSuchAlgorithmException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RSA加密工具类单元测试
 * 
 * <AUTHOR>
 */
@DisplayName("RSA加密工具类测试")
class RsaUtilsTest {

    private static final String TEST_CONTENT = "Hello World!";
    private static final String CHINESE_CONTENT = "中文测试内容，包含各种中文字符：你好世界！";
    private static final String SPECIAL_CHARS = "!@#$%^&*()_+{}|:<>?[]\\;'\",./";
    private static final String LONG_CONTENT = "这是一个较长的测试内容，用于测试RSA加密解密功能的稳定性和正确性。包含中文字符和特殊符号。";

    private String publicKey;
    private String privateKey;

    @BeforeEach
    void setUp() throws NoSuchAlgorithmException {
        // 为每个测试生成新的密钥对
        Map<String, String> keyPair = RsaUtils.genKeyPair();
        publicKey = keyPair.get(RsaUtils.PUBLIC_KEY);
        privateKey = keyPair.get(RsaUtils.PRIVATE_KEY);
    }

    @Nested
    @DisplayName("密钥对生成测试")
    class KeyPairGenerationTests {

        @Test
        @DisplayName("应该成功生成密钥对")
        void shouldGenerateKeyPairSuccessfully() throws NoSuchAlgorithmException {
            // When
            Map<String, String> keyPair = RsaUtils.genKeyPair();

            // Then
            assertNotNull(keyPair, "密钥对不应为null");
            assertEquals(2, keyPair.size(), "密钥对应包含两个元素");
            
            String publicKey = keyPair.get(RsaUtils.PUBLIC_KEY);
            String privateKey = keyPair.get(RsaUtils.PRIVATE_KEY);
            
            assertNotNull(publicKey, "公钥不应为null");
            assertNotNull(privateKey, "私钥不应为null");
            assertFalse(publicKey.isEmpty(), "公钥不应为空");
            assertFalse(privateKey.isEmpty(), "私钥不应为空");
            assertNotEquals(publicKey, privateKey, "公钥和私钥应该不同");
        }

        @Test
        @DisplayName("多次生成的密钥对应该不同")
        void shouldGenerateDifferentKeyPairs() throws NoSuchAlgorithmException {
            // When
            Map<String, String> keyPair1 = RsaUtils.genKeyPair();
            Map<String, String> keyPair2 = RsaUtils.genKeyPair();

            // Then
            assertNotEquals(keyPair1.get(RsaUtils.PUBLIC_KEY), keyPair2.get(RsaUtils.PUBLIC_KEY), 
                "不同次生成的公钥应该不同");
            assertNotEquals(keyPair1.get(RsaUtils.PRIVATE_KEY), keyPair2.get(RsaUtils.PRIVATE_KEY), 
                "不同次生成的私钥应该不同");
        }

        @Test
        @DisplayName("生成的密钥应该是有效的Base64格式")
        void shouldGenerateValidBase64Keys() throws NoSuchAlgorithmException {
            // When
            Map<String, String> keyPair = RsaUtils.genKeyPair();

            // Then
            assertDoesNotThrow(() -> {
                java.util.Base64.getDecoder().decode(keyPair.get(RsaUtils.PUBLIC_KEY));
            }, "公钥应该是有效的Base64格式");

            assertDoesNotThrow(() -> {
                java.util.Base64.getDecoder().decode(keyPair.get(RsaUtils.PRIVATE_KEY));
            }, "私钥应该是有效的Base64格式");
        }
    }

    @Nested
    @DisplayName("RSA加密功能测试")
    class EncryptTests {

        @Test
        @DisplayName("应该成功加密普通字符串")
        void shouldEncryptNormalStringSuccessfully() throws Exception {
            // Given
            String content = TEST_CONTENT;

            // When
            String encrypted = RsaUtils.encrypt(content, publicKey);

            // Then
            assertNotNull(encrypted, "加密结果不应为null");
            assertNotEquals(content, encrypted, "加密后的内容应与原内容不同");
            assertTrue(encrypted.length() > 0, "加密结果长度应大于0");
        }

        @Test
        @DisplayName("应该成功加密中文字符串")
        void shouldEncryptChineseStringSuccessfully() throws Exception {
            // Given
            String content = CHINESE_CONTENT;

            // When
            String encrypted = RsaUtils.encrypt(content, publicKey);

            // Then
            assertNotNull(encrypted, "加密中文内容结果不应为null");
            assertNotEquals(content, encrypted, "加密后的中文内容应与原内容不同");
        }

        @Test
        @DisplayName("应该成功加密包含特殊字符的字符串")
        void shouldEncryptSpecialCharsSuccessfully() throws Exception {
            // Given
            String content = SPECIAL_CHARS;

            // When
            String encrypted = RsaUtils.encrypt(content, publicKey);

            // Then
            assertNotNull(encrypted, "加密特殊字符结果不应为null");
            assertNotEquals(content, encrypted, "加密后的特殊字符应与原内容不同");
        }


        @Test
        @DisplayName("无效的公钥应该抛出异常")
        void shouldThrowExceptionWithInvalidPublicKey() {
            // Given
            String content = TEST_CONTENT;
            String invalidPublicKey = "invalidPublicKey";

            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.encrypt(content, invalidPublicKey);
            }, "无效公钥应该抛出异常");
        }


        @Test
        @DisplayName("null公钥应该抛出异常")
        void shouldThrowExceptionForNullPublicKey() {
            // Given
            String content = TEST_CONTENT;

            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.encrypt(content, null);
            }, "null公钥应该抛出异常");
        }
    }

    @Nested
    @DisplayName("RSA解密功能测试")
    class DecryptTests {

        @Test
        @DisplayName("应该成功解密普通字符串")
        void shouldDecryptNormalStringSuccessfully() throws Exception {
            // Given
            String originalContent = TEST_CONTENT;
            String encrypted = RsaUtils.encrypt(originalContent, publicKey);

            // When
            String decrypted = RsaUtils.decrypt(encrypted, privateKey);

            // Then
            assertNotNull(decrypted, "解密结果不应为null");
            assertEquals(originalContent, decrypted, "解密后应恢复原始内容");
        }

        @Test
        @DisplayName("应该成功解密中文字符串")
        void shouldDecryptChineseStringSuccessfully() throws Exception {
            // Given
            String originalContent = CHINESE_CONTENT;
            String encrypted = RsaUtils.encrypt(originalContent, publicKey);

            // When
            String decrypted = RsaUtils.decrypt(encrypted, privateKey);

            // Then
            assertNotNull(decrypted, "解密中文内容结果不应为null");
            assertEquals(originalContent, decrypted, "解密后应恢复原始中文内容");
        }

        @Test
        @DisplayName("应该成功解密特殊字符")
        void shouldDecryptSpecialCharsSuccessfully() throws Exception {
            // Given
            String originalContent = SPECIAL_CHARS;
            String encrypted = RsaUtils.encrypt(originalContent, publicKey);

            // When
            String decrypted = RsaUtils.decrypt(encrypted, privateKey);

            // Then
            assertNotNull(decrypted, "解密特殊字符结果不应为null");
            assertEquals(originalContent, decrypted, "解密后应恢复原始特殊字符");
        }

        @Test
        @DisplayName("错误的私钥应该解密失败")
        void shouldFailWithWrongPrivateKey() throws Exception {
            // Given
            String originalContent = TEST_CONTENT;
            String encrypted = RsaUtils.encrypt(originalContent, publicKey);
            
            // 生成另一个密钥对获取错误的私钥
            Map<String, String> wrongKeyPair = RsaUtils.genKeyPair();
            String wrongPrivateKey = wrongKeyPair.get(RsaUtils.PRIVATE_KEY);

            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.decrypt(encrypted, wrongPrivateKey);
            }, "错误的私钥应该抛出异常");
        }

        @Test
        @DisplayName("无效的加密内容应该解密失败")
        void shouldFailWithInvalidEncryptedContent() {
            // Given
            String invalidEncrypted = "invalidEncryptedContent";

            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.decrypt(invalidEncrypted, privateKey);
            }, "无效的加密内容应该抛出异常");
        }

        @Test
        @DisplayName("无效的私钥应该抛出异常")
        void shouldThrowExceptionWithInvalidPrivateKey() throws Exception {
            // Given
            String originalContent = TEST_CONTENT;
            String encrypted = RsaUtils.encrypt(originalContent, publicKey);
            String invalidPrivateKey = "invalidPrivateKey";

            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.decrypt(encrypted, invalidPrivateKey);
            }, "无效私钥应该抛出异常");
        }

        @ParameterizedTest
        @DisplayName("null或空值输入应该抛出异常")
        @NullAndEmptySource
        void shouldThrowExceptionForNullOrEmptyEncryptedContent(String encryptedContent) {
            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.decrypt(encryptedContent, privateKey);
            }, "null或空加密内容应该抛出异常");
        }
    }

    @Nested
    @DisplayName("数字签名功能测试")
    class SignatureTests {

        @Test
        @DisplayName("应该成功生成数字签名")
        void shouldGenerateSignatureSuccessfully() throws Exception {
            // Given
            String data = TEST_CONTENT;

            // When
            String signature = RsaUtils.sign(data, privateKey);

            // Then
            assertNotNull(signature, "签名不应为null");
            assertFalse(signature.isEmpty(), "签名不应为空");
            assertNotEquals(data, signature, "签名应与原数据不同");
        }

        @Test
        @DisplayName("应该成功验证数字签名")
        void shouldVerifySignatureSuccessfully() throws Exception {
            // Given
            String data = TEST_CONTENT;
            String signature = RsaUtils.sign(data, privateKey);

            // When
            boolean isValid = RsaUtils.verify(data, publicKey, signature);

            // Then
            assertTrue(isValid, "有效签名应该验证通过");
        }

        @Test
        @DisplayName("相同数据应该产生相同的签名")
        void shouldProduceSameSignatureForSameData() throws Exception {
            // Given
            String data = TEST_CONTENT;

            // When
            String signature1 = RsaUtils.sign(data, privateKey);
            String signature2 = RsaUtils.sign(data, privateKey);

            // Then
            assertEquals(signature1, signature2, "相同数据应产生相同签名");
        }

        @Test
        @DisplayName("不同数据应该产生不同的签名")
        void shouldProduceDifferentSignatureForDifferentData() throws Exception {
            // Given
            String data1 = TEST_CONTENT;
            String data2 = CHINESE_CONTENT;

            // When
            String signature1 = RsaUtils.sign(data1, privateKey);
            String signature2 = RsaUtils.sign(data2, privateKey);

            // Then
            assertNotEquals(signature1, signature2, "不同数据应产生不同签名");
        }

        @Test
        @DisplayName("修改后的数据应该验证失败")
        void shouldFailVerificationWithModifiedData() throws Exception {
            // Given
            String originalData = TEST_CONTENT;
            String modifiedData = TEST_CONTENT + "modified";
            String signature = RsaUtils.sign(originalData, privateKey);

            // When
            boolean isValid = RsaUtils.verify(modifiedData, publicKey, signature);

            // Then
            assertFalse(isValid, "修改后的数据应该验证失败");
        }

        @Test
        @DisplayName("错误的公钥应该验证失败")
        void shouldFailVerificationWithWrongPublicKey() throws Exception {
            // Given
            String data = TEST_CONTENT;
            String signature = RsaUtils.sign(data, privateKey);
            
            // 生成另一个密钥对获取错误的公钥
            Map<String, String> wrongKeyPair = RsaUtils.genKeyPair();
            String wrongPublicKey = wrongKeyPair.get(RsaUtils.PUBLIC_KEY);

            // When
            boolean isValid = RsaUtils.verify(data, wrongPublicKey, signature);

            // Then
            assertFalse(isValid, "错误的公钥应该验证失败");
        }

        @Test
        @DisplayName("无效的签名应该验证失败")
        void shouldFailVerificationWithInvalidSignature() throws Exception {
            // Given
            String data = TEST_CONTENT;
            String invalidSignature = "invalidSignature";

            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.verify(data, publicKey, invalidSignature);
            }, "无效签名应该抛出异常");
        }

        @Test
        @DisplayName("null参数应该抛出异常")
        void shouldThrowExceptionWithNullParameters() {
            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.sign(null, privateKey);
            }, "null数据应该抛出异常");

            assertThrows(Exception.class, () -> {
                RsaUtils.sign(TEST_CONTENT, null);
            }, "null私钥应该抛出异常");

            assertThrows(Exception.class, () -> {
                RsaUtils.verify(null, publicKey, "signature");
            }, "null数据应该抛出异常");

            assertThrows(Exception.class, () -> {
                RsaUtils.verify(TEST_CONTENT, null, "signature");
            }, "null公钥应该抛出异常");

            assertThrows(Exception.class, () -> {
                RsaUtils.verify(TEST_CONTENT, publicKey, null);
            }, "null签名应该抛出异常");
        }
    }

    @Nested
    @DisplayName("PEM格式转换测试")
    class PemConversionTests {

        @Test
        @DisplayName("应该成功转换PKCS8私钥为PEM格式")
        void shouldConvertPkcs8PrivateKeyToPemSuccessfully() throws Exception {
            // When
            String pemPrivateKey = RsaUtils.pkcs8PrivatePem(privateKey);

            // Then
            assertNotNull(pemPrivateKey, "PEM私钥不应为null");
            assertTrue(pemPrivateKey.contains("-----BEGIN RSA PRIVATE KEY-----"), 
                "PEM私钥应包含开始标记");
            assertTrue(pemPrivateKey.contains("-----END RSA PRIVATE KEY-----"), 
                "PEM私钥应包含结束标记");
        }

        @Test
        @DisplayName("应该成功转换PKCS1私钥为PEM格式")
        void shouldConvertPkcs1PrivateKeyToPemSuccessfully() throws Exception {
            // When
            String pemPrivateKey = RsaUtils.pkcs1PrivatePem(privateKey);

            // Then
            assertNotNull(pemPrivateKey, "PEM私钥不应为null");
            assertTrue(pemPrivateKey.contains("-----BEGIN RSA PRIVATE KEY-----"), 
                "PEM私钥应包含开始标记");
            assertTrue(pemPrivateKey.contains("-----END RSA PRIVATE KEY-----"), 
                "PEM私钥应包含结束标记");
        }

        @Test
        @DisplayName("应该成功转换私钥为PEM格式")
        void shouldConvertPrivateKeyToPemSuccessfully() throws Exception {
            // When
            String pemPrivateKey = RsaUtils.privatePem(privateKey);

            // Then
            assertNotNull(pemPrivateKey, "PEM私钥不应为null");
            assertTrue(pemPrivateKey.contains("-----BEGIN RSA PRIVATE KEY-----"), 
                "PEM私钥应包含开始标记");
            assertTrue(pemPrivateKey.contains("-----END RSA PRIVATE KEY-----"), 
                "PEM私钥应包含结束标记");
        }

        @Test
        @DisplayName("无效的私钥应该抛出异常")
        void shouldThrowExceptionWithInvalidPrivateKey() {
            // Given
            String invalidPrivateKey = "invalidPrivateKey";

            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.pkcs8PrivatePem(invalidPrivateKey);
            }, "无效私钥应该抛出异常");

            assertThrows(Exception.class, () -> {
                RsaUtils.pkcs1PrivatePem(invalidPrivateKey);
            }, "无效私钥应该抛出异常");

            assertThrows(Exception.class, () -> {
                RsaUtils.privatePem(invalidPrivateKey);
            }, "无效私钥应该抛出异常");
        }

        @Test
        @DisplayName("null私钥应该抛出异常")
        void shouldThrowExceptionWithNullPrivateKey() {
            // When & Then
            assertThrows(Exception.class, () -> {
                RsaUtils.pkcs8PrivatePem(null);
            }, "null私钥应该抛出异常");

            assertThrows(Exception.class, () -> {
                RsaUtils.pkcs1PrivatePem(null);
            }, "null私钥应该抛出异常");

            assertThrows(Exception.class, () -> {
                RsaUtils.privatePem(null);
            }, "null私钥应该抛出异常");
        }
    }

    @Nested
    @DisplayName("加密解密完整流程测试")
    class EncryptDecryptFlowTests {

        @Test
        @DisplayName("完整的加密解密流程应该保持数据一致性")
        void shouldMaintainDataConsistencyInFullFlow() throws Exception {
            // Given
            String[] testContents = {
                TEST_CONTENT,
                CHINESE_CONTENT,
                SPECIAL_CHARS,
                "1",
                "12345",
                "测试"
            };

            for (String originalContent : testContents) {
                // When
                String encrypted = RsaUtils.encrypt(originalContent, publicKey);
                String decrypted = RsaUtils.decrypt(encrypted, privateKey);

                // Then
                assertNotNull(encrypted, "加密结果不应为null: " + originalContent);
                assertNotNull(decrypted, "解密结果不应为null: " + originalContent);
                assertEquals(originalContent, decrypted, 
                    "完整流程后内容应保持一致: " + originalContent);
            }
        }

        @Test
        @DisplayName("签名验证完整流程应该正确")
        void shouldMaintainSignatureVerificationFlow() throws Exception {
            // Given
            String[] testContents = {
                TEST_CONTENT,
                CHINESE_CONTENT,
                SPECIAL_CHARS,
                LONG_CONTENT
            };

            for (String data : testContents) {
                // When
                String signature = RsaUtils.sign(data, privateKey);
                boolean isValid = RsaUtils.verify(data, publicKey, signature);

                // Then
                assertNotNull(signature, "签名不应为null: " + data);
                assertTrue(isValid, "签名验证应该通过: " + data);
            }
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("单字符加密解密测试")
        void shouldHandleSingleCharacter() throws Exception {
            // Given
            String singleChar = "A";

            // When
            String encrypted = RsaUtils.encrypt(singleChar, publicKey);
            String decrypted = RsaUtils.decrypt(encrypted, privateKey);

            // Then
            assertNotNull(encrypted, "单字符加密不应为null");
            assertNotNull(decrypted, "单字符解密不应为null");
            assertEquals(singleChar, decrypted, "单字符应正确加密解密");
        }

        @Test
        @DisplayName("单字符签名验证测试")
        void shouldHandleSingleCharacterSignature() throws Exception {
            // Given
            String singleChar = "A";

            // When
            String signature = RsaUtils.sign(singleChar, privateKey);
            boolean isValid = RsaUtils.verify(singleChar, publicKey, signature);

            // Then
            assertNotNull(signature, "单字符签名不应为null");
            assertTrue(isValid, "单字符签名验证应该通过");
        }
    }

    @Nested
    @DisplayName("常量验证测试")
    class ConstantTests {

    }
}