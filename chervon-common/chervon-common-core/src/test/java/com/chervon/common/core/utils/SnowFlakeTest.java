package com.chervon.common.core.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SnowFlake雪花算法ID生成器测试类
 * 
 * <AUTHOR> Assistant
 */
@DisplayName("SnowFlake雪花算法ID生成器测试")
class SnowFlakeTest {

    @Nested
    @DisplayName("基础功能测试")
    class BasicFunctionalityTests {

        @Test
        @DisplayName("应该能够生成有效的ID")
        void shouldGenerateValidId() {
            // When
            long id = SnowFlake.nextId();
            
            // Then
            assertTrue(id > 0, "生成的ID应该大于0");
        }

        @Test
        @DisplayName("连续生成的ID应该是递增的")
        void shouldGenerateIncreasingIds() {
            // When
            long id1 = SnowFlake.nextId();
            long id2 = SnowFlake.nextId();
            long id3 = SnowFlake.nextId();
            
            // Then
            assertTrue(id1 < id2, "第二个ID应该大于第一个ID");
            assertTrue(id2 < id3, "第三个ID应该大于第二个ID");
        }

        @RepeatedTest(10)
        @DisplayName("重复测试ID生成的一致性")
        void shouldConsistentlyGenerateValidIds() {
            // When
            long id = SnowFlake.nextId();
            
            // Then
            assertTrue(id > 0, "每次生成的ID都应该大于0");
        }
    }

    @Nested
    @DisplayName("唯一性测试")
    class UniquenessTests {

        @Test
        @DisplayName("批量生成的ID应该都是唯一的")
        void shouldGenerateUniqueIds() {
            // Given
            int count = 10000;
            Set<Long> generatedIds = new HashSet<>(count);
            
            // When
            for (int i = 0; i < count; i++) {
                long id = SnowFlake.nextId();
                generatedIds.add(id);
            }
            
            // Then
            assertEquals(count, generatedIds.size(), "所有生成的ID都应该是唯一的");
        }

        @Test
        @DisplayName("短时间内大量生成的ID应该都是唯一的")
        void shouldGenerateUniqueIdsInShortTime() {
            // Given
            int count = 1000;
            Set<Long> generatedIds = ConcurrentHashMap.newKeySet();
            
            // When
            IntStream.range(0, count)
                    .parallel()
                    .forEach(i -> generatedIds.add(SnowFlake.nextId()));
            
            // Then
            assertEquals(count, generatedIds.size(), "并行生成的ID都应该是唯一的");
        }
    }

    @Nested
    @DisplayName("并发安全测试")
    class ConcurrencyTests {

        @Test
        @DisplayName("多线程并发生成ID应该保证唯一性")
        void shouldGenerateUniqueIdsInMultiThreadEnvironment() throws InterruptedException {
            // Given
            int threadCount = 10;
            int idsPerThread = 1000;
            Set<Long> allIds = ConcurrentHashMap.newKeySet();
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            
            // When
            CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
            for (int i = 0; i < threadCount; i++) {
                futures[i] = CompletableFuture.runAsync(() -> {
                    for (int j = 0; j < idsPerThread; j++) {
                        long id = SnowFlake.nextId();
                        allIds.add(id);
                    }
                }, executor);
            }
            
            CompletableFuture.allOf(futures).join();
            executor.shutdown();
            assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS), "线程池应该正常关闭");
            
            // Then
            int expectedTotalIds = threadCount * idsPerThread;
            assertEquals(expectedTotalIds, allIds.size(), 
                    String.format("多线程生成的%d个ID都应该是唯一的", expectedTotalIds));
        }

        @Test
        @DisplayName("高并发场景下ID生成的性能测试")
        void shouldHandleHighConcurrencyPerformance() throws InterruptedException {
            // Given
            int threadCount = 20;
            int idsPerThread = 500;
            Set<Long> allIds = ConcurrentHashMap.newKeySet();
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            long startTime = System.currentTimeMillis();
            
            // When
            CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
            for (int i = 0; i < threadCount; i++) {
                futures[i] = CompletableFuture.runAsync(() -> {
                    for (int j = 0; j < idsPerThread; j++) {
                        long id = SnowFlake.nextId();
                        allIds.add(id);
                    }
                }, executor);
            }
            
            CompletableFuture.allOf(futures).join();
            executor.shutdown();
            assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS), "线程池应该正常关闭");
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // Then
            int expectedTotalIds = threadCount * idsPerThread;
            assertEquals(expectedTotalIds, allIds.size(), "高并发下生成的ID都应该是唯一的");
            assertTrue(duration < 5000, "高并发ID生成应该在5秒内完成，实际耗时: " + duration + "ms");
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("连续快速生成ID应该处理序列号溢出")
        void shouldHandleSequenceOverflow() {
            // Given & When
            Set<Long> ids = new HashSet<>();
            
            // 快速生成大量ID来测试序列号处理
            for (int i = 0; i < 5000; i++) {
                long id = SnowFlake.nextId();
                ids.add(id);
            }
            
            // Then
            assertEquals(5000, ids.size(), "即使快速生成大量ID，也应该保证唯一性");
        }

        @Test
        @DisplayName("ID应该包含时间戳信息")
        void shouldContainTimestampInfo() {
            // Given
            long beforeTime = System.currentTimeMillis();
            
            // When
            long id = SnowFlake.nextId();
            
            // Then
            assertTrue(id > 0, "ID应该大于0");
            // 雪花算法生成的ID应该包含时间戳信息，所以ID会很大
            assertTrue(id > beforeTime, "ID应该包含时间戳信息，因此会比当前时间戳大");
        }
    }

    @Nested
    @DisplayName("异常场景测试")
    class ExceptionTests {

        @Test
        @DisplayName("静态方法调用应该始终成功")
        void shouldAlwaysSucceedWithStaticMethodCall() {
            // When & Then
            assertDoesNotThrow(() -> {
                for (int i = 0; i < 100; i++) {
                    long id = SnowFlake.nextId();
                    assertTrue(id > 0, "每次调用都应该成功生成有效ID");
                }
            }, "静态方法调用不应该抛出异常");
        }

        @Test
        @DisplayName("长时间运行应该保持稳定")
        void shouldRemainStableOverLongPeriod() {
            // Given
            Set<Long> ids = new HashSet<>();
            
            // When
            assertDoesNotThrow(() -> {
                for (int i = 0; i < 10000; i++) {
                    long id = SnowFlake.nextId();
                    ids.add(id);
                    
                    // 每1000次暂停1毫秒，模拟长时间运行
                    if (i % 1000 == 0) {
                        Thread.sleep(1);
                    }
                }
            }, "长时间运行不应该出现异常");
            
            // Then
            assertEquals(10000, ids.size(), "长时间运行应该保持ID唯一性");
        }
    }

    @Nested
    @DisplayName("性能基准测试")
    class PerformanceTests {

        @Test
        @DisplayName("单线程性能基准测试")
        void shouldMeetSingleThreadPerformanceBenchmark() {
            // Given
            int count = 100000;
            long startTime = System.currentTimeMillis();
            
            // When
            for (int i = 0; i < count; i++) {
                SnowFlake.nextId();
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // Then
            assertTrue(duration < 1000, 
                    String.format("生成%d个ID应该在1秒内完成，实际耗时: %dms", count, duration));
            
            double idsPerSecond = (double) count / duration * 1000;
            assertTrue(idsPerSecond > 50000, 
                    String.format("单线程每秒应该能生成超过50000个ID，实际: %.0f", idsPerSecond));
        }
    }
}