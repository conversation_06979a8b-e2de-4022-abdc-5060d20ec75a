package com.chervon.common.core.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 转换工具类单元测试
 * 
 * <AUTHOR>
 */
@DisplayName("转换工具类测试")
class ConvertUtilTest {

    // 测试用的源对象类
    public static class SourceUser {
        private String name;
        private Integer age;
        private String email;

        public SourceUser() {}

        public SourceUser(String name, Integer age, String email) {
            this.name = name;
            this.age = age;
            this.email = email;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SourceUser that = (SourceUser) o;
            return Objects.equals(name, that.name) &&
                   Objects.equals(age, that.age) &&
                   Objects.equals(email, that.email);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, age, email);
        }
    }

    // 测试用的目标对象类
    public static class TargetUser {
        private String name;
        private Integer age;
        private String email;

        public TargetUser() {}

        public TargetUser(String name, Integer age, String email) {
            this.name = name;
            this.age = age;
            this.email = email;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TargetUser that = (TargetUser) o;
            return Objects.equals(name, that.name) &&
                   Objects.equals(age, that.age) &&
                   Objects.equals(email, that.email);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, age, email);
        }
    }

    // 不同类型的测试类
    public static class DifferentTypeUser {
        private String name;
        private String age; // 注意这里是String类型
        private String email;

        public DifferentTypeUser() {}

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getAge() { return age; }
        public void setAge(String age) { this.age = age; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    // 无默认构造函数的类（用于测试异常情况）
    public static class NoDefaultConstructorUser {
        private String name;
        
        public NoDefaultConstructorUser(String name) {
            this.name = name;
        }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    private List<SourceUser> sourceUserList;
    private SourceUser sourceUser;

    @BeforeEach
    void setUp() {
        sourceUser = new SourceUser("张三", 25, "<EMAIL>");
        
        sourceUserList = Arrays.asList(
            new SourceUser("张三", 25, "<EMAIL>"),
            new SourceUser("李四", 30, "<EMAIL>"),
            new SourceUser("王五", 28, "<EMAIL>")
        );
    }

    @Nested
    @DisplayName("列表转换功能测试")
    class ConvertListTests {

        @Test
        @DisplayName("应该成功转换相同类型的列表")
        void shouldConvertListWithSameType() {
            // When
            List<TargetUser> result = ConvertUtil.convertList(sourceUserList, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals(3, result.size(), "转换后列表大小应该一致");
            
            for (int i = 0; i < sourceUserList.size(); i++) {
                SourceUser source = sourceUserList.get(i);
                TargetUser target = result.get(i);
                
                assertEquals(source.getName(), target.getName(), "姓名应该一致");
                assertEquals(source.getAge(), target.getAge(), "年龄应该一致");
                assertEquals(source.getEmail(), target.getEmail(), "邮箱应该一致");
            }
        }

        @Test
        @DisplayName("空列表应该返回空列表")
        void shouldReturnEmptyListForEmptyInput() {
            // Given
            List<SourceUser> emptyList = new ArrayList<>();

            // When
            List<TargetUser> result = ConvertUtil.convertList(emptyList, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertTrue(result.isEmpty(), "空列表转换后应该仍为空");
        }

        @Test
        @DisplayName("null列表应该返回空列表")
        void shouldReturnEmptyListForNullInput() {
            // When
            List<TargetUser> result = ConvertUtil.convertList(null, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertTrue(result.isEmpty(), "null列表转换后应该返回空列表");
        }

        @Test
        @DisplayName("包含null元素的列表应该跳过null元素")
        void shouldSkipNullElementsInList() {
            // Given
            List<SourceUser> listWithNull = Arrays.asList(
                new SourceUser("张三", 25, "<EMAIL>"),
                null,
                new SourceUser("李四", 30, "<EMAIL>")
            );

            // When
            List<TargetUser> result = ConvertUtil.convertList(listWithNull, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals(2, result.size(), "应该跳过null元素，只转换有效元素");
            assertEquals("张三", result.get(0).getName(), "第一个元素应该正确转换");
            assertEquals("李四", result.get(1).getName(), "第二个元素应该正确转换");
        }

        @Test
        @DisplayName("目标类无默认构造函数时应该处理异常")
        void shouldHandleExceptionForNoDefaultConstructor() {
            // When
            List<NoDefaultConstructorUser> result = ConvertUtil.convertList(sourceUserList, NoDefaultConstructorUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertTrue(result.isEmpty(), "无法实例化的类应该返回空列表");
        }
    }

    @Nested
    @DisplayName("不同类型列表转换功能测试")
    class ConvertDifferentTypesListTests {

        @Test
        @DisplayName("应该成功转换不同类型的列表")
        void shouldConvertDifferentTypesList() {
            // When
            List<DifferentTypeUser> result = ConvertUtil.convertDifferentTypesList(sourceUserList, DifferentTypeUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals(3, result.size(), "转换后列表大小应该一致");
            
            for (int i = 0; i < sourceUserList.size(); i++) {
                SourceUser source = sourceUserList.get(i);
                DifferentTypeUser target = result.get(i);
                
                assertEquals(source.getName(), target.getName(), "姓名应该一致");
                assertEquals(source.getAge().toString(), target.getAge(), "年龄应该转换为字符串");
                assertEquals(source.getEmail(), target.getEmail(), "邮箱应该一致");
            }
        }

        @Test
        @DisplayName("null列表应该返回null")
        void shouldReturnNullForNullInput() {
            // When
            List<DifferentTypeUser> result = ConvertUtil.convertDifferentTypesList(null, DifferentTypeUser.class);

            // Then
            assertNull(result, "null输入应该返回null");
        }

        @Test
        @DisplayName("空列表应该返回空列表")
        void shouldReturnEmptyListForEmptyInput() {
            // Given
            List<SourceUser> emptyList = new ArrayList<>();

            // When
            List<DifferentTypeUser> result = ConvertUtil.convertDifferentTypesList(emptyList, DifferentTypeUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertTrue(result.isEmpty(), "空列表转换后应该仍为空");
        }
    }

    @Nested
    @DisplayName("单对象转换功能测试")
    class ConvertSingleObjectTests {

        @Test
        @DisplayName("应该成功转换单个对象")
        void shouldConvertSingleObject() {
            // When
            TargetUser result = ConvertUtil.convert(sourceUser, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals(sourceUser.getName(), result.getName(), "姓名应该一致");
            assertEquals(sourceUser.getAge(), result.getAge(), "年龄应该一致");
            assertEquals(sourceUser.getEmail(), result.getEmail(), "邮箱应该一致");
        }

        @Test
        @DisplayName("null对象应该返回null")
        void shouldReturnNullForNullInput() {
            // When
            TargetUser result = ConvertUtil.convert(null, TargetUser.class);

            // Then
            assertNull(result, "null输入应该返回null");
        }

        @Test
        @DisplayName("目标类无默认构造函数时应该返回null")
        void shouldReturnNullForNoDefaultConstructor() {
            // When
            NoDefaultConstructorUser result = ConvertUtil.convert(sourceUser, NoDefaultConstructorUser.class);

            // Then
            assertNull(result, "无法实例化的类应该返回null");
        }

        @Test
        @DisplayName("应该正确处理部分属性匹配的情况")
        void shouldHandlePartialPropertyMatch() {
            // Given - 创建一个只有部分属性的源对象
            SourceUser partialSource = new SourceUser();
            partialSource.setName("测试用户");
            // 不设置age和email

            // When
            TargetUser result = ConvertUtil.convert(partialSource, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals("测试用户", result.getName(), "已设置的属性应该正确转换");
            assertNull(result.getAge(), "未设置的属性应该为null");
            assertNull(result.getEmail(), "未设置的属性应该为null");
        }
    }

    @Nested
    @DisplayName("Map到对象转换功能测试")
    class MapToObjectTests {

        @Test
        @DisplayName("应该成功将Map转换为对象")
        void shouldConvertMapToObject() throws Exception {
            // Given
            Map<String, Object> map = new HashMap<>();
            map.put("name", "张三");
            map.put("age", 25);
            map.put("email", "<EMAIL>");

            // When
            TargetUser result = (TargetUser) ConvertUtil.mapToObject(map, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals("张三", result.getName(), "姓名应该正确转换");
            assertEquals(25, result.getAge(), "年龄应该正确转换");
            assertEquals("<EMAIL>", result.getEmail(), "邮箱应该正确转换");
        }

        @Test
        @DisplayName("null Map应该返回null")
        void shouldReturnNullForNullMap() throws Exception {
            // When
            Object result = ConvertUtil.mapToObject(null, TargetUser.class);

            // Then
            assertNull(result, "null Map应该返回null");
        }

        @Test
        @DisplayName("空Map应该返回空对象")
        void shouldReturnEmptyObjectForEmptyMap() throws Exception {
            // Given
            Map<String, Object> emptyMap = new HashMap<>();

            // When
            TargetUser result = (TargetUser) ConvertUtil.mapToObject(emptyMap, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertNull(result.getName(), "未设置的属性应该为null");
            assertNull(result.getAge(), "未设置的属性应该为null");
            assertNull(result.getEmail(), "未设置的属性应该为null");
        }

        @Test
        @DisplayName("应该正确处理类型转换")
        void shouldHandleTypeConversion() throws Exception {
            // Given
            Map<String, Object> map = new HashMap<>();
            map.put("name", "李四");
            map.put("age", "30"); // 字符串类型的数字
            map.put("email", "<EMAIL>");

            // When
            TargetUser result = (TargetUser) ConvertUtil.mapToObject(map, TargetUser.class);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals("李四", result.getName(), "姓名应该正确转换");
            assertEquals(Integer.valueOf(30), result.getAge(), "字符串数字应该正确转换为Integer");
            assertEquals("<EMAIL>", result.getEmail(), "邮箱应该正确转换");
        }

        @Test
        @DisplayName("目标类无默认构造函数时应该抛出异常")
        void shouldThrowExceptionForNoDefaultConstructor() {
            // Given
            Map<String, Object> map = new HashMap<>();
            map.put("name", "测试");

            // When & Then
            assertThrows(Exception.class, () -> {
                ConvertUtil.mapToObject(map, NoDefaultConstructorUser.class);
            }, "无默认构造函数的类应该抛出异常");
        }
    }

    @Nested
    @DisplayName("对象到Map转换功能测试")
    class ObjectToMapTests {

        @Test
        @DisplayName("应该成功将对象转换为Map")
        void shouldConvertObjectToMap() {
            // When
            Map<?, ?> result = ConvertUtil.objectToMap(sourceUser);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals("张三", result.get("name"), "姓名应该正确转换");
            assertEquals(25, result.get("age"), "年龄应该正确转换");
            assertEquals("<EMAIL>", result.get("email"), "邮箱应该正确转换");
        }

        @Test
        @DisplayName("null对象应该返回null")
        void shouldReturnNullForNullObject() {
            // When
            Map<?, ?> result = ConvertUtil.objectToMap(null);

            // Then
            assertNull(result, "null对象应该返回null");
        }

        @Test
        @DisplayName("应该包含对象的所有属性")
        void shouldIncludeAllObjectProperties() {
            // When
            Map<?, ?> result = ConvertUtil.objectToMap(sourceUser);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertTrue(result.containsKey("name"), "应该包含name属性");
            assertTrue(result.containsKey("age"), "应该包含age属性");
            assertTrue(result.containsKey("email"), "应该包含email属性");
            assertTrue(result.containsKey("class"), "应该包含class属性");
        }

        @Test
        @DisplayName("应该正确处理null属性值")
        void shouldHandleNullPropertyValues() {
            // Given
            SourceUser userWithNulls = new SourceUser();
            userWithNulls.setName("测试用户");
            // age和email保持为null

            // When
            Map<?, ?> result = ConvertUtil.objectToMap(userWithNulls);

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals("测试用户", result.get("name"), "非null属性应该正确转换");
            assertNull(result.get("age"), "null属性应该保持为null");
            assertNull(result.get("email"), "null属性应该保持为null");
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("大量数据转换性能测试")
        void shouldHandleLargeDataConversion() {
            // Given
            List<SourceUser> largeList = new ArrayList<>();
            for (int i = 0; i < 1000; i++) {
                largeList.add(new SourceUser("用户" + i, 20 + (i % 50), "user" + i + "@example.com"));
            }

            // When
            long startTime = System.currentTimeMillis();
            List<TargetUser> result = ConvertUtil.convertList(largeList, TargetUser.class);
            long endTime = System.currentTimeMillis();

            // Then
            assertNotNull(result, "转换结果不应为null");
            assertEquals(1000, result.size(), "转换后数量应该一致");
            assertTrue(endTime - startTime < 1000, "大量数据转换应该在合理时间内完成");
        }

        @Test
        @DisplayName("复杂对象转换测试")
        void shouldHandleComplexObjectConversion() {
            // Given - 创建包含复杂属性的对象
            Map<String, Object> complexMap = new HashMap<>();
            complexMap.put("name", "复杂用户");
            complexMap.put("age", 35);
            complexMap.put("email", "<EMAIL>");
            
            List<String> hobbies = Arrays.asList("阅读", "游泳", "编程");
            complexMap.put("hobbies", hobbies);

            // When & Then - 应该能够处理而不抛出异常
            assertDoesNotThrow(() -> {
                ConvertUtil.mapToObject(complexMap, TargetUser.class);
            }, "复杂对象转换不应该抛出异常");
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("转换过程中的异常应该被正确处理")
        void shouldHandleConversionExceptions() {
            // 测试各种可能导致异常的情况都不应该抛出未捕获的异常
            assertDoesNotThrow(() -> {
                ConvertUtil.convertList(sourceUserList, NoDefaultConstructorUser.class);
            }, "无默认构造函数不应抛出未捕获异常");

            assertDoesNotThrow(() -> {
                ConvertUtil.convert(sourceUser, NoDefaultConstructorUser.class);
            }, "单对象转换异常不应抛出未捕获异常");
        }

        @Test
        @DisplayName("类型不匹配时应该正确处理")
        void shouldHandleTypeMismatch() {
            // Given - 创建类型不完全匹配的Map
            Map<String, Object> mismatchMap = new HashMap<>();
            mismatchMap.put("name", 12345); // 数字而不是字符串
            mismatchMap.put("age", "not_a_number"); // 无法转换为数字的字符串

            // When & Then - 应该能够处理而不抛出异常
            assertDoesNotThrow(() -> {
                ConvertUtil.mapToObject(mismatchMap, TargetUser.class);
            }, "类型不匹配不应该抛出未捕获异常");
        }
    }

    @Nested
    @DisplayName("数据完整性测试")
    class DataIntegrityTests {

        @Test
        @DisplayName("转换后数据应该保持完整性")
        void shouldMaintainDataIntegrity() {
            // Given
            SourceUser original = new SourceUser("数据完整性测试", 99, "<EMAIL>");

            // When
            TargetUser converted = ConvertUtil.convert(original, TargetUser.class);
            Map<?, ?> mapConverted = ConvertUtil.objectToMap(converted);

            // Then
            assertNotNull(converted, "转换结果不应为null");
            assertNotNull(mapConverted, "Map转换结果不应为null");
            
            assertEquals(original.getName(), converted.getName(), "转换后姓名应该一致");
            assertEquals(original.getAge(), converted.getAge(), "转换后年龄应该一致");
            assertEquals(original.getEmail(), converted.getEmail(), "转换后邮箱应该一致");
            
            assertEquals(converted.getName(), mapConverted.get("name"), "Map中姓名应该一致");
            assertEquals(converted.getAge(), mapConverted.get("age"), "Map中年龄应该一致");
            assertEquals(converted.getEmail(), mapConverted.get("email"), "Map中邮箱应该一致");
        }

        @Test
        @DisplayName("循环转换应该保持数据一致性")
        void shouldMaintainConsistencyInCircularConversion() throws Exception {
            // Given
            SourceUser original = new SourceUser("循环测试", 88, "<EMAIL>");

            // When - 进行循环转换：对象 -> Map -> 对象
            Map<?, ?> map = ConvertUtil.objectToMap(original);
            @SuppressWarnings("unchecked")
            Map<String, Object> stringMap = (Map<String, Object>) map;
            SourceUser restored = (SourceUser) ConvertUtil.mapToObject(stringMap, SourceUser.class);

            // Then
            assertNotNull(restored, "恢复的对象不应为null");
            assertEquals(original.getName(), restored.getName(), "循环转换后姓名应该一致");
            assertEquals(original.getAge(), restored.getAge(), "循环转换后年龄应该一致");
            assertEquals(original.getEmail(), restored.getEmail(), "循环转换后邮箱应该一致");
        }
    }
}