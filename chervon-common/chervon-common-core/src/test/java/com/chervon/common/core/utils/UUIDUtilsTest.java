package com.chervon.common.core.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UUID工具类单元测试
 * 
 * <AUTHOR>
 */
@DisplayName("UUID工具类测试")
class UUIDUtilsTest {

    @Nested
    @DisplayName("标准UUID生成测试")
    class RandomUUIDTests {

        @Test
        @DisplayName("应该生成32位UUID字符串")
        void shouldGenerate32CharUUID() {
            // When
            String uuid = UUIDUtils.randomUUID();

            // Then
            assertNotNull(uuid, "UUID不应为null");
            assertEquals(32, uuid.length(), "UUID长度应为32位");
        }

        @Test
        @DisplayName("生成的UUID应该只包含十六进制字符")
        void shouldContainOnlyHexCharacters() {
            // Given
            Pattern hexPattern = Pattern.compile("^[a-f0-9]+$");

            // When
            String uuid = UUIDUtils.randomUUID();

            // Then
            assertTrue(hexPattern.matcher(uuid).matches(), 
                "UUID应该只包含小写十六进制字符: " + uuid);
        }

        @Test
        @DisplayName("生成的UUID不应包含连字符")
        void shouldNotContainHyphens() {
            // When
            String uuid = UUIDUtils.randomUUID();

            // Then
            assertFalse(uuid.contains("-"), "UUID不应包含连字符");
        }

        @Test
        @DisplayName("多次生成的UUID应该不同")
        void shouldGenerateDifferentUUIDs() {
            // When
            String uuid1 = UUIDUtils.randomUUID();
            String uuid2 = UUIDUtils.randomUUID();

            // Then
            assertNotEquals(uuid1, uuid2, "多次生成的UUID应该不同");
        }

        @Test
        @DisplayName("大量生成的UUID应该具有高唯一性")
        void shouldHaveHighUniqueness() {
            // Given
            Set<String> uuids = new HashSet<>();
            int generateCount = 10000;

            // When
            for (int i = 0; i < generateCount; i++) {
                String uuid = UUIDUtils.randomUUID();
                uuids.add(uuid);
            }

            // Then
            assertEquals(generateCount, uuids.size(), 
                "生成的UUID应该完全唯一，实际唯一数量: " + uuids.size() + "/" + generateCount);
        }

        @Test
        @DisplayName("UUID格式应该符合预期")
        void shouldHaveExpectedFormat() {
            // When
            String uuid = UUIDUtils.randomUUID();

            // Then
            assertNotNull(uuid, "UUID不应为null");
            assertEquals(32, uuid.length(), "UUID长度应为32");
            assertTrue(uuid.matches("^[a-f0-9]{32}$"), 
                "UUID应该是32位小写十六进制字符串: " + uuid);
        }

        @Test
        @DisplayName("UUID应该包含数字和字母")
        void shouldContainDigitsAndLetters() {
            // Given
            boolean hasDigit = false;
            boolean hasLetter = false;
            
            // When - 生成多个UUID增加包含数字和字母的概率
            for (int i = 0; i < 10; i++) {
                String uuid = UUIDUtils.randomUUID();
                
                for (char c : uuid.toCharArray()) {
                    if (Character.isDigit(c)) {
                        hasDigit = true;
                    }
                    if (Character.isLetter(c)) {
                        hasLetter = true;
                    }
                }
                
                if (hasDigit && hasLetter) {
                    break;
                }
            }

            // Then
            assertTrue(hasDigit || hasLetter, "UUID应该包含数字或字母");
        }
    }

    @Nested
    @DisplayName("短UUID生成测试")
    class RandomShortUUIDTests {

        @Test
        @DisplayName("应该生成20位短UUID字符串")
        void shouldGenerate20CharShortUUID() {
            // When
            String shortUUID = UUIDUtils.randomShortUUID();

            // Then
            assertNotNull(shortUUID, "短UUID不应为null");
            assertEquals(20, shortUUID.length(), "短UUID长度应为20位");
        }

        @Test
        @DisplayName("生成的短UUID应该只包含64进制字符")
        void shouldContainOnly64BaseCharacters() {
            // Given
            String validChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
            
            // When
            String shortUUID = UUIDUtils.randomShortUUID();

            // Then
            for (char c : shortUUID.toCharArray()) {
                assertTrue(validChars.indexOf(c) >= 0, 
                    "短UUID包含无效字符: " + c + " in " + shortUUID);
            }
        }

        @Test
        @DisplayName("多次生成的短UUID应该不同")
        void shouldGenerateDifferentShortUUIDs() {
            // When
            String shortUUID1 = UUIDUtils.randomShortUUID();
            String shortUUID2 = UUIDUtils.randomShortUUID();

            // Then
            assertNotEquals(shortUUID1, shortUUID2, "多次生成的短UUID应该不同");
        }

        @Test
        @DisplayName("大量生成的短UUID应该具有高唯一性")
        void shouldHaveHighUniquenessForShortUUID() {
            // Given
            Set<String> shortUUIDs = new HashSet<>();
            int generateCount = 10000;

            // When
            for (int i = 0; i < generateCount; i++) {
                String shortUUID = UUIDUtils.randomShortUUID();
                shortUUIDs.add(shortUUID);
            }

            // Then
            // 短UUID由于长度较短，可能会有极少的重复，但应该保持很高的唯一性
            assertTrue(shortUUIDs.size() > generateCount * 0.99, 
                "短UUID应该有很高的唯一性，实际唯一数量: " + shortUUIDs.size() + "/" + generateCount);
        }

        @Test
        @DisplayName("短UUID应该包含大写字母、小写字母和数字")
        void shouldContainUppercaseLowercaseAndDigits() {
            // Given
            boolean hasUppercase = false;
            boolean hasLowercase = false;
            boolean hasDigit = false;
            boolean hasSpecial = false; // +/

            // When - 生成多个短UUID增加包含各种字符的概率
            for (int i = 0; i < 50; i++) {
                String shortUUID = UUIDUtils.randomShortUUID();
                
                for (char c : shortUUID.toCharArray()) {
                    if (Character.isUpperCase(c)) {
                        hasUppercase = true;
                    } else if (Character.isLowerCase(c)) {
                        hasLowercase = true;
                    } else if (Character.isDigit(c)) {
                        hasDigit = true;
                    } else if (c == '+' || c == '/') {
                        hasSpecial = true;
                    }
                }
                
                if (hasUppercase && hasLowercase && hasDigit) {
                    break;
                }
            }

            // Then
            assertTrue(hasUppercase || hasLowercase || hasDigit, 
                "短UUID应该包含大写字母、小写字母或数字中的至少一种");
        }

        @Test
        @DisplayName("短UUID格式应该符合预期")
        void shouldHaveExpectedShortUUIDFormat() {
            // Given
            Pattern base64Pattern = Pattern.compile("^[A-Za-z0-9+/]{20}$");

            // When
            String shortUUID = UUIDUtils.randomShortUUID();

            // Then
            assertTrue(base64Pattern.matcher(shortUUID).matches(), 
                "短UUID应该是20位Base64字符串: " + shortUUID);
        }
    }

    @Nested
    @DisplayName("64进制字典测试")
    class DigitsArrayTests {

        @Test
        @DisplayName("64进制字典应该包含64个字符")
        void shouldHave64Characters() {
            // Then
            assertEquals(64, UUIDUtils.digits.length, "64进制字典应该包含64个字符");
        }

        @Test
        @DisplayName("64进制字典应该包含预期的字符集")
        void shouldContainExpectedCharacterSet() {
            // Given
            String expectedChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
            
            // Then
            assertEquals(expectedChars.length(), UUIDUtils.digits.length, "字符数量应该一致");
            
            for (int i = 0; i < expectedChars.length(); i++) {
                assertEquals(expectedChars.charAt(i), UUIDUtils.digits[i], 
                    "位置" + i + "的字符应该一致");
            }
        }

        @Test
        @DisplayName("64进制字典中的字符应该唯一")
        void shouldHaveUniqueCharacters() {
            // Given
            Set<Character> uniqueChars = new HashSet<>();
            
            // When
            for (char c : UUIDUtils.digits) {
                uniqueChars.add(c);
            }

            // Then
            assertEquals(UUIDUtils.digits.length, uniqueChars.size(), 
                "64进制字典中的字符应该唯一");
        }

        @Test
        @DisplayName("64进制字典应该包含大写字母A-Z")
        void shouldContainUppercaseLetters() {
            // Given
            String digits = new String(UUIDUtils.digits);
            
            // Then
            for (char c = 'A'; c <= 'Z'; c++) {
                assertTrue(digits.indexOf(c) >= 0, "应该包含大写字母: " + c);
            }
        }

        @Test
        @DisplayName("64进制字典应该包含小写字母a-z")
        void shouldContainLowercaseLetters() {
            // Given
            String digits = new String(UUIDUtils.digits);
            
            // Then
            for (char c = 'a'; c <= 'z'; c++) {
                assertTrue(digits.indexOf(c) >= 0, "应该包含小写字母: " + c);
            }
        }

        @Test
        @DisplayName("64进制字典应该包含数字0-9")
        void shouldContainDigits() {
            // Given
            String digits = new String(UUIDUtils.digits);
            
            // Then
            for (char c = '0'; c <= '9'; c++) {
                assertTrue(digits.indexOf(c) >= 0, "应该包含数字: " + c);
            }
        }

        @Test
        @DisplayName("64进制字典应该包含特殊字符+和/")
        void shouldContainSpecialCharacters() {
            // Given
            String digits = new String(UUIDUtils.digits);
            
            // Then
            assertTrue(digits.indexOf('+') >= 0, "应该包含字符+");
            assertTrue(digits.indexOf('/') >= 0, "应该包含字符/");
        }
    }

    @Nested
    @DisplayName("性能测试")
    class PerformanceTests {

        @Test
        @DisplayName("标准UUID生成性能测试")
        void shouldGenerateUUIDsEfficiently() {
            // Given
            int iterations = 10000;
            long startTime = System.currentTimeMillis();

            // When
            for (int i = 0; i < iterations; i++) {
                String uuid = UUIDUtils.randomUUID();
                assertNotNull(uuid, "UUID不应为null");
            }

            // Then
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            assertTrue(duration < 1000, 
                "UUID生成性能测试：" + iterations + "次生成耗时" + duration + "ms，应该小于1000ms");
        }

        @Test
        @DisplayName("短UUID生成性能测试")
        void shouldGenerateShortUUIDsEfficiently() {
            // Given
            int iterations = 10000;
            long startTime = System.currentTimeMillis();

            // When
            for (int i = 0; i < iterations; i++) {
                String shortUUID = UUIDUtils.randomShortUUID();
                assertNotNull(shortUUID, "短UUID不应为null");
            }

            // Then
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            assertTrue(duration < 1000, 
                "短UUID生成性能测试：" + iterations + "次生成耗时" + duration + "ms，应该小于1000ms");
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("连续生成UUID应该保持稳定性")
        void shouldMaintainStabilityInContinuousGeneration() {
            // Given
            int iterations = 1000;

            // When & Then
            for (int i = 0; i < iterations; i++) {
                String uuid = UUIDUtils.randomUUID();
                String shortUUID = UUIDUtils.randomShortUUID();
                
                assertNotNull(uuid, "第" + i + "次生成的UUID不应为null");
                assertNotNull(shortUUID, "第" + i + "次生成的短UUID不应为null");
                assertEquals(32, uuid.length(), "第" + i + "次生成的UUID长度应为32");
                assertEquals(20, shortUUID.length(), "第" + i + "次生成的短UUID长度应为20");
            }
        }

        @Test
        @DisplayName("验证UUID的随机性分布")
        void shouldHaveRandomDistribution() {
            // Given
            int[] charCounts = new int[16]; // 十六进制字符0-f的计数
            int sampleSize = 1000;

            // When
            for (int i = 0; i < sampleSize; i++) {
                String uuid = UUIDUtils.randomUUID();
                for (char c : uuid.toCharArray()) {
                    if (c >= '0' && c <= '9') {
                        charCounts[c - '0']++;
                    } else if (c >= 'a' && c <= 'f') {
                        charCounts[c - 'a' + 10]++;
                    }
                }
            }

            // Then - 验证每个字符都有出现（随机性检查）
            for (int i = 0; i < 16; i++) {
                assertTrue(charCounts[i] > 0, 
                    "十六进制字符" + Integer.toHexString(i) + "应该在随机生成中出现");
            }
        }

        @Test
        @DisplayName("验证短UUID的随机性分布")
        void shouldHaveRandomDistributionForShortUUID() {
            // Given
            Set<Character> usedChars = new HashSet<>();
            int sampleSize = 1000;

            // When
            for (int i = 0; i < sampleSize; i++) {
                String shortUUID = UUIDUtils.randomShortUUID();
                for (char c : shortUUID.toCharArray()) {
                    usedChars.add(c);
                }
            }

            // Then - 验证使用了多种不同的字符
            assertTrue(usedChars.size() > 32, 
                "短UUID应该使用多种不同字符，实际使用字符数: " + usedChars.size());
        }
    }

    @Nested
    @DisplayName("并发安全测试")
    class ConcurrencyTests {

        @Test
        @DisplayName("多线程生成UUID应该是安全的")
        void shouldBeSafeInMultiThreadedEnvironment() throws InterruptedException {
            // Given
            Set<String> uuids = new HashSet<>();
            Set<String> shortUUIDs = new HashSet<>();
            int threadCount = 10;
            int iterationsPerThread = 100;
            Thread[] threads = new Thread[threadCount];

            // When
            for (int i = 0; i < threadCount; i++) {
                threads[i] = new Thread(() -> {
                    for (int j = 0; j < iterationsPerThread; j++) {
                        synchronized (uuids) {
                            uuids.add(UUIDUtils.randomUUID());
                        }
                        synchronized (shortUUIDs) {
                            shortUUIDs.add(UUIDUtils.randomShortUUID());
                        }
                    }
                });
                threads[i].start();
            }

            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join();
            }

            // Then
            int expectedCount = threadCount * iterationsPerThread;
            assertEquals(expectedCount, uuids.size(), 
                "多线程生成的UUID应该完全唯一");
            assertTrue(shortUUIDs.size() > expectedCount * 0.99, 
                "多线程生成的短UUID应该有很高的唯一性");
        }
    }
}