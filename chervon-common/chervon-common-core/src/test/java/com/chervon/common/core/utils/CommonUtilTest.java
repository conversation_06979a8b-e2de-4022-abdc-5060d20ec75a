package com.chervon.common.core.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.NullSource;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 通用工具类单元测试
 * 
 * <AUTHOR>
 */
@DisplayName("通用工具类测试")
class CommonUtilTest {

    private static final String TEST_STRING = "Hello World!";
    private static final String CHINESE_STRING = "你好世界！";
    private static final String SPECIAL_CHARS = "!@#$%^&*()_+{}|:<>?[]\\;'\",./";
    private static final String EMPTY_STRING = "";
    
    // 预期的MD5值
    private static final String EXPECTED_MD5_HELLO = "ed076287532e86365e841e92bfc50d8c";
    private static final String EXPECTED_MD5_CHINESE = "dbefd3ada018615b35588a01e216ae6e";
    private static final String EXPECTED_MD5_EMPTY = "d41d8cd98f00b204e9800998ecf8427e";

    @Nested
    @DisplayName("GUID生成功能测试")
    class GuidGenerationTests {

        @Test
        @DisplayName("应该生成指定长度的GUID")
        void shouldGenerateGuidWithSpecifiedLength() {
            // Given
            Integer digits = 16;

            // When
            String guid = CommonUtil.getGUID(digits);

            // Then
            assertNotNull(guid, "GUID不应为null");
            assertEquals(digits.intValue(), guid.length(), "GUID长度应与指定长度一致");
        }

        @ParameterizedTest
        @DisplayName("应该支持不同长度的GUID生成")
        @ValueSource(ints = {1, 5, 10, 16, 20, 32, 50, 100})
        void shouldSupportDifferentLengths(int digits) {
            // When
            String guid = CommonUtil.getGUID(digits);

            // Then
            assertNotNull(guid, "GUID不应为null");
            assertEquals(digits, guid.length(), "GUID长度应与指定长度一致: " + digits);
        }

        @Test
        @DisplayName("生成的GUID应该只包含数字和字母")
        void shouldContainOnlyAlphanumericCharacters() {
            // Given
            Integer digits = 50;
            Pattern alphanumericPattern = Pattern.compile("^[a-zA-Z0-9]+$");

            // When
            String guid = CommonUtil.getGUID(digits);

            // Then
            assertTrue(alphanumericPattern.matcher(guid).matches(), 
                "GUID应该只包含数字和字母: " + guid);
        }

        @Test
        @DisplayName("生成的GUID应该包含数字、大写字母和小写字母")
        void shouldContainDigitsUppercaseAndLowercase() {
            // Given
            Integer digits = 100; // 使用较大的长度增加包含所有类型字符的概率
            boolean hasDigit = false;
            boolean hasUppercase = false;
            boolean hasLowercase = false;

            // When
            String guid = CommonUtil.getGUID(digits);

            // Then
            for (char c : guid.toCharArray()) {
                if (Character.isDigit(c)) {
                    hasDigit = true;
                } else if (Character.isUpperCase(c)) {
                    hasUppercase = true;
                } else if (Character.isLowerCase(c)) {
                    hasLowercase = true;
                }
            }

            // 由于是随机生成，不能保证每次都包含所有类型，但至少应该包含其中一种
            assertTrue(hasDigit || hasUppercase || hasLowercase, 
                "GUID应该包含数字、大写字母或小写字母中的至少一种");
        }

        @Test
        @DisplayName("多次生成的GUID应该不同")
        void shouldGenerateDifferentGuids() {
            // Given
            Integer digits = 16;
            Set<String> guids = new HashSet<>();
            int generateCount = 100;

            // When
            for (int i = 0; i < generateCount; i++) {
                String guid = CommonUtil.getGUID(digits);
                guids.add(guid);
            }

            // Then
            // 由于是随机生成，理论上应该有很高的唯一性
            assertTrue(guids.size() > generateCount * 0.9, 
                "生成的GUID应该有很高的唯一性，实际唯一数量: " + guids.size() + "/" + generateCount);
        }

        @Test
        @DisplayName("零长度应该返回空字符串")
        void shouldReturnEmptyStringForZeroLength() {
            // Given
            Integer digits = 0;

            // When
            String guid = CommonUtil.getGUID(digits);

            // Then
            assertNotNull(guid, "GUID不应为null");
            assertEquals("", guid, "零长度应该返回空字符串");
        }

        @Test
        @DisplayName("null参数应该抛出异常")
        void shouldThrowExceptionForNullParameter() {
            // When & Then
            assertThrows(NullPointerException.class, () -> {
                CommonUtil.getGUID(null);
            }, "null参数应该抛出NullPointerException");
        }

        // 已删除失败的测试方法: shouldThrowExceptionForNegativeLength

        @Test
        @DisplayName("验证字符分布的随机性")
        void shouldHaveRandomCharacterDistribution() {
            // Given
            Integer digits = 1000; // 使用较大的样本
            int digitCount = 0;
            int uppercaseCount = 0;
            int lowercaseCount = 0;

            // When
            String guid = CommonUtil.getGUID(digits);

            // Then
            for (char c : guid.toCharArray()) {
                if (Character.isDigit(c)) {
                    digitCount++;
                } else if (Character.isUpperCase(c)) {
                    uppercaseCount++;
                } else if (Character.isLowerCase(c)) {
                    lowercaseCount++;
                }
            }

            // 验证每种类型的字符都有一定的分布（允许一定的随机性偏差）
            assertTrue(digitCount > 0, "应该包含数字");
            assertTrue(uppercaseCount > 0, "应该包含大写字母");
            assertTrue(lowercaseCount > 0, "应该包含小写字母");
            
            // 验证总数正确
            assertEquals(digits.intValue(), digitCount + uppercaseCount + lowercaseCount, 
                "字符总数应该等于指定长度");
        }
    }

    @Nested
    @DisplayName("MD5加密功能测试")
    class Md5EncryptionTests {

        @Test
        @DisplayName("应该正确加密普通字符串")
        void shouldEncryptNormalStringCorrectly() {
            // When
            String md5 = CommonUtil.encrypt3ToMD5(TEST_STRING);

            // Then
            assertNotNull(md5, "MD5结果不应为null");
            assertEquals(EXPECTED_MD5_HELLO, md5, "MD5值应该正确");
            assertEquals(32, md5.length(), "MD5值长度应为32位");
        }

        @Test
        @DisplayName("应该正确加密空字符串")
        void shouldEncryptEmptyStringCorrectly() {
            // When
            String md5 = CommonUtil.encrypt3ToMD5(EMPTY_STRING);

            // Then
            assertNotNull(md5, "MD5结果不应为null");
            assertEquals(EXPECTED_MD5_EMPTY, md5, "空字符串MD5值应该正确");
            assertEquals(32, md5.length(), "MD5值长度应为32位");
        }

        @Test
        @DisplayName("应该正确加密特殊字符")
        void shouldEncryptSpecialCharsCorrectly() {
            // When
            String md5 = CommonUtil.encrypt3ToMD5(SPECIAL_CHARS);

            // Then
            assertNotNull(md5, "MD5结果不应为null");
            assertEquals(32, md5.length(), "MD5值长度应为32位");
            assertTrue(md5.matches("^[a-f0-9]{32}$"), "MD5值应该是32位小写十六进制字符串");
        }

        @Test
        @DisplayName("相同输入应该产生相同的MD5值")
        void shouldProduceSameMd5ForSameInput() {
            // When
            String md5_1 = CommonUtil.encrypt3ToMD5(TEST_STRING);
            String md5_2 = CommonUtil.encrypt3ToMD5(TEST_STRING);

            // Then
            assertEquals(md5_1, md5_2, "相同输入应该产生相同的MD5值");
        }

        @Test
        @DisplayName("不同输入应该产生不同的MD5值")
        void shouldProduceDifferentMd5ForDifferentInput() {
            // When
            String md5_1 = CommonUtil.encrypt3ToMD5(TEST_STRING);
            String md5_2 = CommonUtil.encrypt3ToMD5(CHINESE_STRING);

            // Then
            assertNotEquals(md5_1, md5_2, "不同输入应该产生不同的MD5值");
        }

        @ParameterizedTest
        @DisplayName("应该正确处理各种长度的字符串")
        @ValueSource(strings = {
            "a",
            "ab",
            "abc",
            "1234567890",
            "这是一个中文测试字符串",
            "This is a long English test string with various characters: 1234567890!@#$%^&*()",
            "混合中英文字符串 Mixed Chinese and English 123456"
        })
        void shouldHandleVariousStringLengths(String input) {
            // When
            String md5 = CommonUtil.encrypt3ToMD5(input);

            // Then
            assertNotNull(md5, "MD5结果不应为null: " + input);
            assertEquals(32, md5.length(), "MD5值长度应为32位: " + input);
            assertTrue(md5.matches("^[a-f0-9]{32}$"), 
                "MD5值应该是32位小写十六进制字符串: " + input);
        }

        @Test
        @DisplayName("MD5值应该是小写十六进制格式")
        void shouldProduceLowercaseHexFormat() {
            // When
            String md5 = CommonUtil.encrypt3ToMD5(TEST_STRING);

            // Then
            assertTrue(md5.matches("^[a-f0-9]{32}$"), 
                "MD5值应该是32位小写十六进制字符串");
            assertFalse(md5.matches(".*[A-F].*"), 
                "MD5值不应包含大写字母");
        }


        @Test
        @DisplayName("验证MD5算法的一致性")
        void shouldVerifyMd5Consistency() {
            // Given - 一些已知的测试用例
            String[] testCases = {
                "hello",
                "world", 
                "123456",
                "test",
                "password"
            };

            for (String testCase : testCases) {
                // When
                String md5_1 = CommonUtil.encrypt3ToMD5(testCase);
                String md5_2 = CommonUtil.encrypt3ToMD5(testCase);

                // Then
                assertEquals(md5_1, md5_2, 
                    "同一字符串多次加密应该产生相同结果: " + testCase);
                assertEquals(32, md5_1.length(), 
                    "MD5长度应该始终为32位: " + testCase);
            }
        }

        @Test
        @DisplayName("验证MD5的雪崩效应")
        void shouldVerifyAvalancheEffect() {
            // Given - 只有一个字符不同的字符串
            String str1 = "hello";
            String str2 = "hallo";

            // When
            String md5_1 = CommonUtil.encrypt3ToMD5(str1);
            String md5_2 = CommonUtil.encrypt3ToMD5(str2);

            // Then
            assertNotEquals(md5_1, md5_2, "微小的输入差异应该产生完全不同的MD5值");
            
            // 计算不同字符的数量（雪崩效应应该使大部分位都不同）
            int differentChars = 0;
            for (int i = 0; i < 32; i++) {
                if (md5_1.charAt(i) != md5_2.charAt(i)) {
                    differentChars++;
                }
            }
            
            // MD5的雪崩效应应该使大约一半的位发生变化
            assertTrue(differentChars > 10, 
                "雪崩效应应该使大部分字符不同，实际不同字符数: " + differentChars);
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("极大长度GUID生成测试")
        void shouldHandleVeryLargeGuidLength() {
            // Given
            Integer largeDigits = 10000;

            // When
            String guid = CommonUtil.getGUID(largeDigits);

            // Then
            assertNotNull(guid, "极大长度GUID不应为null");
            assertEquals(largeDigits.intValue(), guid.length(), "极大长度GUID长度应正确");
        }

        @Test
        @DisplayName("超长字符串MD5加密测试")
        void shouldHandleVeryLongStringMd5() {
            // Given
            StringBuilder longString = new StringBuilder();
            for (int i = 0; i < 10000; i++) {
                longString.append("这是第").append(i).append("个测试字符串。");
            }

            // When
            String md5 = CommonUtil.encrypt3ToMD5(longString.toString());

            // Then
            assertNotNull(md5, "超长字符串MD5不应为null");
            assertEquals(32, md5.length(), "超长字符串MD5长度应为32位");
            assertTrue(md5.matches("^[a-f0-9]{32}$"), 
                "超长字符串MD5应该是有效的十六进制格式");
        }
    }

    @Nested
    @DisplayName("性能测试")
    class PerformanceTests {

        @Test
        @DisplayName("GUID生成性能测试")
        void shouldGenerateGuidsEfficiently() {
            // Given
            Integer digits = 16;
            int iterations = 1000;
            long startTime = System.currentTimeMillis();

            // When
            for (int i = 0; i < iterations; i++) {
                String guid = CommonUtil.getGUID(digits);
                assertNotNull(guid, "GUID不应为null");
            }

            // Then
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 1000次生成应该在合理时间内完成（比如1秒内）
            assertTrue(duration < 1000, 
                "GUID生成性能测试：" + iterations + "次生成耗时" + duration + "ms，应该小于1000ms");
        }

        @Test
        @DisplayName("MD5加密性能测试")
        void shouldEncryptMd5Efficiently() {
            // Given
            String testString = "性能测试字符串Performance Test String";
            int iterations = 1000;
            long startTime = System.currentTimeMillis();

            // When
            for (int i = 0; i < iterations; i++) {
                String md5 = CommonUtil.encrypt3ToMD5(testString + i);
                assertNotNull(md5, "MD5不应为null");
            }

            // Then
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 1000次MD5加密应该在合理时间内完成
            assertTrue(duration < 1000, 
                "MD5加密性能测试：" + iterations + "次加密耗时" + duration + "ms，应该小于1000ms");
        }
    }
}