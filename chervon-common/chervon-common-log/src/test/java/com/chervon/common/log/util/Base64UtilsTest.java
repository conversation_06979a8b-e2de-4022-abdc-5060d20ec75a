package com.chervon.common.log.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Base64Utils工具类测试
 * 
 * <AUTHOR> Assistant
 */
@DisplayName("Base64Utils工具类测试")
class Base64UtilsTest {

    private static final String TEST_STRING = "Hello, World! 你好世界！";
    private static final byte[] TEST_BYTES = TEST_STRING.getBytes(StandardCharsets.UTF_8);
    private static final String EMPTY_STRING = "";
    private static final byte[] EMPTY_BYTES = new byte[0];

    @Nested
    @DisplayName("标准Base64编码测试")
    class StandardEncodeTests {

        @Test
        @DisplayName("应该正确编码字节数组")
        void shouldEncodeByteArrayCorrectly() {
            // Given & When
            byte[] encoded = Base64Utils.encode(TEST_BYTES);
            String expectedEncoded = Base64.getEncoder().encodeToString(TEST_BYTES);

            // Then
            assertNotNull(encoded, "编码结果不应该为null");
            assertEquals(expectedEncoded, new String(encoded, StandardCharsets.UTF_8), 
                    "编码结果应该与标准Base64编码一致");
        }

        @Test
        @DisplayName("应该处理空字节数组")
        void shouldHandleEmptyByteArray() {
            // Given & When
            byte[] encoded = Base64Utils.encode(EMPTY_BYTES);

            // Then
            assertNotNull(encoded, "编码结果不应该为null");
            assertSame(EMPTY_BYTES, encoded, "空字节数组应该返回原数组");
        }

        @Test
        @DisplayName("应该正确编码为字符串")
        void shouldEncodeToStringCorrectly() {
            // Given & When
            String encoded = Base64Utils.encodeToString(TEST_BYTES);
            String expectedEncoded = Base64.getEncoder().encodeToString(TEST_BYTES);

            // Then
            assertNotNull(encoded, "编码结果不应该为null");
            assertEquals(expectedEncoded, encoded, "编码结果应该与标准Base64编码一致");
        }

        @Test
        @DisplayName("应该处理空字节数组编码为字符串")
        void shouldHandleEmptyByteArrayToString() {
            // Given & When
            String encoded = Base64Utils.encodeToString(EMPTY_BYTES);

            // Then
            assertEquals(EMPTY_STRING, encoded, "空字节数组编码应该返回空字符串");
        }

        @Test
        @DisplayName("应该处理包含特殊字符的数据")
        void shouldHandleSpecialCharacters() {
            // Given
            String specialChars = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~\n\t\r";
            byte[] specialBytes = specialChars.getBytes(StandardCharsets.UTF_8);

            // When
            String encoded = Base64Utils.encodeToString(specialBytes);
            String expectedEncoded = Base64.getEncoder().encodeToString(specialBytes);

            // Then
            assertEquals(expectedEncoded, encoded, "应该正确编码特殊字符");
        }
    }

    @Nested
    @DisplayName("标准Base64解码测试")
    class StandardDecodeTests {

        @Test
        @DisplayName("应该正确解码字节数组")
        void shouldDecodeByteArrayCorrectly() {
            // Given
            byte[] encoded = Base64Utils.encode(TEST_BYTES);

            // When
            byte[] decoded = Base64Utils.decode(encoded);

            // Then
            assertNotNull(decoded, "解码结果不应该为null");
            assertArrayEquals(TEST_BYTES, decoded, "解码结果应该与原始数据一致");
        }

        @Test
        @DisplayName("应该处理空字节数组解码")
        void shouldHandleEmptyByteArrayDecode() {
            // Given & When
            byte[] decoded = Base64Utils.decode(EMPTY_BYTES);

            // Then
            assertNotNull(decoded, "解码结果不应该为null");
            assertSame(EMPTY_BYTES, decoded, "空字节数组应该返回原数组");
        }

        @Test
        @DisplayName("应该正确从字符串解码")
        void shouldDecodeFromStringCorrectly() {
            // Given
            String encoded = Base64Utils.encodeToString(TEST_BYTES);

            // When
            byte[] decoded = Base64Utils.decodeFromString(encoded);

            // Then
            assertNotNull(decoded, "解码结果不应该为null");
            assertArrayEquals(TEST_BYTES, decoded, "解码结果应该与原始数据一致");
        }

        @Test
        @DisplayName("应该处理空字符串解码")
        void shouldHandleEmptyStringDecode() {
            // Given & When
            byte[] decoded = Base64Utils.decodeFromString(EMPTY_STRING);

            // Then
            assertNotNull(decoded, "解码结果不应该为null");
            assertEquals(0, decoded.length, "空字符串解码应该返回空字节数组");
        }

        @Test
        @DisplayName("编码解码应该是可逆的")
        void shouldBeReversible() {
            // Given
            String originalText = "This is a test string with 中文字符 and symbols: !@#$%^&*()";
            byte[] originalBytes = originalText.getBytes(StandardCharsets.UTF_8);

            // When
            String encoded = Base64Utils.encodeToString(originalBytes);
            byte[] decoded = Base64Utils.decodeFromString(encoded);
            String decodedText = new String(decoded, StandardCharsets.UTF_8);

            // Then
            assertEquals(originalText, decodedText, "编码解码应该是可逆的");
        }
    }

    @Nested
    @DisplayName("URL安全Base64编码测试")
    class UrlSafeEncodeTests {

        @Test
        @DisplayName("应该正确进行URL安全编码")
        void shouldEncodeUrlSafeCorrectly() {
            // Given
            String urlUnsafeString = "subjects?_d=1";
            byte[] urlUnsafeBytes = urlUnsafeString.getBytes(StandardCharsets.UTF_8);

            // When
            byte[] encoded = Base64Utils.encodeUrlSafe(urlUnsafeBytes);
            String expectedEncoded = Base64.getUrlEncoder().encodeToString(urlUnsafeBytes);

            // Then
            assertNotNull(encoded, "URL安全编码结果不应该为null");
            assertEquals(expectedEncoded, new String(encoded, StandardCharsets.UTF_8), 
                    "URL安全编码结果应该与标准URL安全编码一致");
        }

        @Test
        @DisplayName("应该处理空字节数组的URL安全编码")
        void shouldHandleEmptyByteArrayUrlSafeEncode() {
            // Given & When
            byte[] encoded = Base64Utils.encodeUrlSafe(EMPTY_BYTES);

            // Then
            assertNotNull(encoded, "编码结果不应该为null");
            assertSame(EMPTY_BYTES, encoded, "空字节数组应该返回原数组");
        }

        @Test
        @DisplayName("应该正确进行URL安全编码为字符串")
        void shouldEncodeToUrlSafeStringCorrectly() {
            // Given
            String urlUnsafeString = "subjects?_d=1&test=value";
            byte[] urlUnsafeBytes = urlUnsafeString.getBytes(StandardCharsets.UTF_8);

            // When
            String encoded = Base64Utils.encodeToUrlSafeString(urlUnsafeBytes);
            String expectedEncoded = Base64.getUrlEncoder().encodeToString(urlUnsafeBytes);

            // Then
            assertNotNull(encoded, "URL安全编码结果不应该为null");
            assertEquals(expectedEncoded, encoded, "URL安全编码结果应该与标准URL安全编码一致");
        }

        @Test
        @DisplayName("URL安全编码不应该包含+和/字符")
        void shouldNotContainPlusAndSlashInUrlSafeEncoding() {
            // Given
            byte[] dataWithPlusSlash = new byte[]{(byte) 0xFF, (byte) 0xFE, (byte) 0xFD};

            // When
            String urlSafeEncoded = Base64Utils.encodeToUrlSafeString(dataWithPlusSlash);
            String standardEncoded = Base64Utils.encodeToString(dataWithPlusSlash);

            // Then
            assertFalse(urlSafeEncoded.contains("+"), "URL安全编码不应该包含+字符");
            assertFalse(urlSafeEncoded.contains("/"), "URL安全编码不应该包含/字符");
            
            // 标准编码可能包含这些字符
            if (standardEncoded.contains("+") || standardEncoded.contains("/")) {
                assertNotEquals(standardEncoded, urlSafeEncoded, "URL安全编码应该与标准编码不同");
            }
        }
    }

    @Nested
    @DisplayName("URL安全Base64解码测试")
    class UrlSafeDecodeTests {

        @Test
        @DisplayName("应该正确进行URL安全解码")
        void shouldDecodeUrlSafeCorrectly() {
            // Given
            String originalString = "subjects?_d=1&test=value";
            byte[] originalBytes = originalString.getBytes(StandardCharsets.UTF_8);
            byte[] encoded = Base64Utils.encodeUrlSafe(originalBytes);

            // When
            byte[] decoded = Base64Utils.decodeUrlSafe(encoded);

            // Then
            assertNotNull(decoded, "URL安全解码结果不应该为null");
            assertArrayEquals(originalBytes, decoded, "URL安全解码结果应该与原始数据一致");
        }

        @Test
        @DisplayName("应该处理空字节数组的URL安全解码")
        void shouldHandleEmptyByteArrayUrlSafeDecode() {
            // Given & When
            byte[] decoded = Base64Utils.decodeUrlSafe(EMPTY_BYTES);

            // Then
            assertNotNull(decoded, "解码结果不应该为null");
            assertSame(EMPTY_BYTES, decoded, "空字节数组应该返回原数组");
        }

        @Test
        @DisplayName("应该正确从URL安全字符串解码")
        void shouldDecodeFromUrlSafeStringCorrectly() {
            // Given
            String originalString = "subjects?_d=1&test=value";
            byte[] originalBytes = originalString.getBytes(StandardCharsets.UTF_8);
            String encoded = Base64Utils.encodeToUrlSafeString(originalBytes);

            // When
            byte[] decoded = Base64Utils.decodeFromUrlSafeString(encoded);

            // Then
            assertNotNull(decoded, "URL安全解码结果不应该为null");
            assertArrayEquals(originalBytes, decoded, "URL安全解码结果应该与原始数据一致");
        }

        @Test
        @DisplayName("URL安全编码解码应该是可逆的")
        void shouldBeReversibleForUrlSafe() {
            // Given
            String originalText = "This is a URL unsafe string: +/=?&test=value";
            byte[] originalBytes = originalText.getBytes(StandardCharsets.UTF_8);

            // When
            String encoded = Base64Utils.encodeToUrlSafeString(originalBytes);
            byte[] decoded = Base64Utils.decodeFromUrlSafeString(encoded);
            String decodedText = new String(decoded, StandardCharsets.UTF_8);

            // Then
            assertEquals(originalText, decodedText, "URL安全编码解码应该是可逆的");
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("应该处理单字节数据")
        void shouldHandleSingleByte() {
            // Given
            byte[] singleByte = {42};

            // When
            String encoded = Base64Utils.encodeToString(singleByte);
            byte[] decoded = Base64Utils.decodeFromString(encoded);

            // Then
            assertArrayEquals(singleByte, decoded, "应该正确处理单字节数据");
        }

        @Test
        @DisplayName("应该处理大数据量")
        void shouldHandleLargeData() {
            // Given
            byte[] largeData = new byte[10000];
            for (int i = 0; i < largeData.length; i++) {
                largeData[i] = (byte) (i % 256);
            }

            // When
            String encoded = Base64Utils.encodeToString(largeData);
            byte[] decoded = Base64Utils.decodeFromString(encoded);

            // Then
            assertArrayEquals(largeData, decoded, "应该正确处理大数据量");
        }

        @Test
        @DisplayName("应该处理所有可能的字节值")
        void shouldHandleAllByteValues() {
            // Given
            byte[] allBytes = new byte[256];
            for (int i = 0; i < 256; i++) {
                allBytes[i] = (byte) i;
            }

            // When
            String encoded = Base64Utils.encodeToString(allBytes);
            byte[] decoded = Base64Utils.decodeFromString(encoded);

            // Then
            assertArrayEquals(allBytes, decoded, "应该正确处理所有可能的字节值");
        }

        @Test
        @DisplayName("应该处理Unicode字符")
        void shouldHandleUnicodeCharacters() {
            // Given
            String unicodeString = "🌟🚀💻🎉 Unicode测试 العربية русский 日本語";
            byte[] unicodeBytes = unicodeString.getBytes(StandardCharsets.UTF_8);

            // When
            String encoded = Base64Utils.encodeToString(unicodeBytes);
            byte[] decoded = Base64Utils.decodeFromString(encoded);
            String decodedString = new String(decoded, StandardCharsets.UTF_8);

            // Then
            assertEquals(unicodeString, decodedString, "应该正确处理Unicode字符");
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("应该处理null输入")
        void shouldHandleNullInput() {
            // Given & When & Then
            assertThrows(NullPointerException.class, () -> Base64Utils.encode(null),
                    "encode方法应该对null输入抛出NullPointerException");
            
            assertThrows(NullPointerException.class, () -> Base64Utils.decode(null),
                    "decode方法应该对null输入抛出NullPointerException");
            
            assertThrows(NullPointerException.class, () -> Base64Utils.encodeUrlSafe(null),
                    "encodeUrlSafe方法应该对null输入抛出NullPointerException");
            
            assertThrows(NullPointerException.class, () -> Base64Utils.decodeUrlSafe(null),
                    "decodeUrlSafe方法应该对null输入抛出NullPointerException");
            
            assertThrows(NullPointerException.class, () -> Base64Utils.encodeToString(null),
                    "encodeToString方法应该对null输入抛出NullPointerException");
            
            assertThrows(NullPointerException.class, () -> Base64Utils.decodeFromString(null),
                    "decodeFromString方法应该对null输入抛出NullPointerException");
            
            assertThrows(NullPointerException.class, () -> Base64Utils.encodeToUrlSafeString(null),
                    "encodeToUrlSafeString方法应该对null输入抛出NullPointerException");
            
            assertThrows(NullPointerException.class, () -> Base64Utils.decodeFromUrlSafeString(null),
                    "decodeFromUrlSafeString方法应该对null输入抛出NullPointerException");
        }

        @Test
        @DisplayName("应该处理无效的Base64字符串")
        void shouldHandleInvalidBase64String() {
            // Given
            String invalidBase64 = "This is not a valid base64 string!";

            // When & Then
            assertThrows(IllegalArgumentException.class, 
                    () -> Base64Utils.decodeFromString(invalidBase64),
                    "无效的Base64字符串应该抛出IllegalArgumentException");
        }

        @Test
        @DisplayName("应该处理无效的URL安全Base64字符串")
        void shouldHandleInvalidUrlSafeBase64String() {
            // Given
            String invalidUrlSafeBase64 = "This+is/not=valid";

            // When & Then
            assertThrows(IllegalArgumentException.class, 
                    () -> Base64Utils.decodeFromUrlSafeString(invalidUrlSafeBase64),
                    "无效的URL安全Base64字符串应该抛出IllegalArgumentException");
        }
    }

    @Nested
    @DisplayName("性能测试")
    class PerformanceTests {

        @Test
        @DisplayName("批量编码解码性能测试")
        void shouldPerformWellForBatchOperations() {
            // Given
            byte[] testData = "Performance test data".getBytes(StandardCharsets.UTF_8);

            // When
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < 10000; i++) {
                String encoded = Base64Utils.encodeToString(testData);
                Base64Utils.decodeFromString(encoded);
            }
            long endTime = System.currentTimeMillis();

            // Then
            long duration = endTime - startTime;
            assertTrue(duration < 1000, "批量编码解码应该在1秒内完成，实际耗时: " + duration + "ms");
        }

        @Test
        @DisplayName("URL安全编码解码性能测试")
        void shouldPerformWellForUrlSafeOperations() {
            // Given
            byte[] testData = "URL safe performance test data".getBytes(StandardCharsets.UTF_8);

            // When
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < 10000; i++) {
                String encoded = Base64Utils.encodeToUrlSafeString(testData);
                Base64Utils.decodeFromUrlSafeString(encoded);
            }
            long endTime = System.currentTimeMillis();

            // Then
            long duration = endTime - startTime;
            assertTrue(duration < 1000, "URL安全批量编码解码应该在1秒内完成，实际耗时: " + duration + "ms");
        }
    }

    @Nested
    @DisplayName("兼容性测试")
    class CompatibilityTests {

        @Test
        @DisplayName("应该与标准Java Base64兼容")
        void shouldBeCompatibleWithStandardJavaBase64() {
            // Given
            String testString = "Compatibility test string 兼容性测试";
            byte[] testBytes = testString.getBytes(StandardCharsets.UTF_8);

            // When
            String ourEncoded = Base64Utils.encodeToString(testBytes);
            String javaEncoded = Base64.getEncoder().encodeToString(testBytes);
            
            byte[] ourDecoded = Base64Utils.decodeFromString(javaEncoded);
            byte[] javaDecoded = Base64.getDecoder().decode(ourEncoded);

            // Then
            assertEquals(javaEncoded, ourEncoded, "编码结果应该与标准Java Base64一致");
            assertArrayEquals(testBytes, ourDecoded, "应该能解码标准Java Base64编码的数据");
            assertArrayEquals(testBytes, javaDecoded, "标准Java Base64应该能解码我们编码的数据");
        }

        @Test
        @DisplayName("应该与标准Java URL安全Base64兼容")
        void shouldBeCompatibleWithStandardJavaUrlSafeBase64() {
            // Given
            String testString = "URL safe compatibility test 兼容性测试";
            byte[] testBytes = testString.getBytes(StandardCharsets.UTF_8);

            // When
            String ourEncoded = Base64Utils.encodeToUrlSafeString(testBytes);
            String javaEncoded = Base64.getUrlEncoder().encodeToString(testBytes);
            
            byte[] ourDecoded = Base64Utils.decodeFromUrlSafeString(javaEncoded);
            byte[] javaDecoded = Base64.getUrlDecoder().decode(ourEncoded);

            // Then
            assertEquals(javaEncoded, ourEncoded, "URL安全编码结果应该与标准Java Base64一致");
            assertArrayEquals(testBytes, ourDecoded, "应该能解码标准Java URL安全Base64编码的数据");
            assertArrayEquals(testBytes, javaDecoded, "标准Java URL安全Base64应该能解码我们编码的数据");
        }
    }
}