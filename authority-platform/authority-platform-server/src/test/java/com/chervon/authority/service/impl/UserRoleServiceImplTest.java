package com.chervon.authority.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.authority.api.exception.AuthorityErrorCode;
import com.chervon.authority.api.exception.AuthorityException;
import com.chervon.authority.api.service.RemoteDataLimitRefreshService;
import com.chervon.authority.config.ExceptionMessageUtil;
import com.chervon.authority.domain.dto.role.UserRoleDto;
import com.chervon.authority.domain.dto.role.UserRoleItem;
import com.chervon.authority.domain.dto.role.UserRolePageDto;
import com.chervon.authority.domain.dto.role.UserRoleUnbindDto;
import com.chervon.authority.domain.entity.Role;
import com.chervon.authority.domain.entity.UserRole;
import com.chervon.authority.domain.vo.UserRoleDetailVo;
import com.chervon.authority.domain.vo.UserRoleVo;
import com.chervon.authority.mapper.UserRoleMapper;
import com.chervon.authority.service.PlatformService;
import com.chervon.authority.service.RoleService;
import com.chervon.authority.service.RpcService;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.usercenter.api.service.OrganizationQueryService;
import com.chervon.usercenter.api.service.RemoteUserRoleService;
import com.chervon.usercenter.api.service.SysUserQueryService;
import com.chervon.usercenter.api.vo.SysUserVo;
import com.chervon.usercenter.api.vo.UserRoleAddVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UserRoleServiceImpl 单元测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-05-27
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户角色服务实现类测试")
class UserRoleServiceImplTest {

    @Mock
    private RoleService roleService;

    @Mock
    private PlatformService platformService;

    @Mock
    private RpcService rpcService;

    @Mock
    private SysUserQueryService userQueryService;

    @Mock
    private OrganizationQueryService organizationQueryService;

    @Mock
    private RemoteUserRoleService remoteUserRoleService;

    @Mock
    private RemoteDataLimitRefreshService remoteDataLimitRefreshService;

    @Mock
    private UserRoleMapper userRoleMapper;

    @InjectMocks
    private UserRoleServiceImpl userRoleService;

    private UserRole testUserRole;
    private Role testRole;
    private UserRoleDto testUserRoleDto;
    private UserRoleItem testUserRoleItem;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUserRole = new UserRole();
        testUserRole.setId(1L);
        testUserRole.setRoleId(100L);
        testUserRole.setAuthorizedGuid("user-guid-001");
        testUserRole.setType(0);
        testUserRole.setDescription("测试用户角色");

        testRole = new Role();
        testRole.setId(100L);
        testRole.setRoleName("测试角色");
        testRole.setRoleType(1);
        testRole.setRoleStatus(CommonConstant.ONE);
        testRole.setPlatformId(1L);

        testUserRoleItem = new UserRoleItem();
        testUserRoleItem.setAuthorizedGuid("user-guid-001");
        testUserRoleItem.setType(0);

        testUserRoleDto = new UserRoleDto();
        testUserRoleDto.setRoleId(100L);
        testUserRoleDto.setDescription("测试描述");
        testUserRoleDto.setUserRoleItems(Arrays.asList(testUserRoleItem));
    }

    @Test
    @DisplayName("编辑用户角色 - 正常情况")
    void testEdit_Success() {
        // Given
        List<UserRole> oldUserRoles = Arrays.asList(testUserRole);

        UserRoleServiceImpl spyService = spy(userRoleService);
        doReturn(oldUserRoles).when(spyService).listByRoleId(100L);
        doReturn(true).when(spyService).removeBatchByIds(anyList());
        doReturn(true).when(spyService).saveBatch(anyList());

        when(roleService.getById(100L)).thenReturn(testRole);

        // When
        assertDoesNotThrow(() -> spyService.edit(testUserRoleDto));

        // Then
        verify(spyService, times(1)).removeBatchByIds(oldUserRoles);
        verify(spyService, times(1)).saveBatch(anyList());
        verify(remoteDataLimitRefreshService, times(1)).refresh();
    }

    @Test
    @DisplayName("分页查询用户角色 - 空结果")
    void testGetPage_EmptyResult() {
        // Given
        UserRolePageDto pageDto = new UserRolePageDto();
        pageDto.setPageNum(1L);
        pageDto.setPageSize(10L);

        Page<UserRole> mockPage = new Page<>(1, 10);
        mockPage.setRecords(new ArrayList<>());
        mockPage.setTotal(0L);

        UserRoleServiceImpl spyService = spy(userRoleService);
        doReturn(mockPage).when(spyService).page(any(Page.class), any(LambdaQueryWrapper.class));

        // When
        PageResult<UserRoleVo> result = spyService.getPage(pageDto);

        // Then
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0L, result.getTotal());
    }

    @Test
    @DisplayName("解绑用户角色 - 正常情况")
    void testUnbind_Success() {
        // Given
        UserRoleUnbindDto unbindDto = new UserRoleUnbindDto();
        unbindDto.setRoleId(100L);
        unbindDto.setAuthorizedGuid("user-guid-001");

        UserRoleServiceImpl spyService = spy(userRoleService);
        doReturn(testUserRole).when(spyService).getOne(any(LambdaQueryWrapper.class));
        doReturn(true).when(spyService).removeById(testUserRole);

        when(roleService.getById(100L)).thenReturn(testRole);

        // When
        assertDoesNotThrow(() -> spyService.unbind(unbindDto));

        // Then
        verify(spyService, times(1)).removeById(testUserRole);
        verify(remoteDataLimitRefreshService, times(1)).refresh();
    }

    @Test
    @DisplayName("根据角色ID查询用户角色列表")
    void testListByRoleId() {
        // Given
        List<UserRole> expectedUserRoles = Arrays.asList(testUserRole);

        UserRoleServiceImpl spyService = spy(userRoleService);
        doReturn(expectedUserRoles).when(spyService).list(any(LambdaQueryWrapper.class));

        // When
        List<UserRole> result = spyService.listByRoleId(100L);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testUserRole, result.get(0));
    }

    @Test
    @DisplayName("获取角色名称列表 - 空组织列表")
    void testGetRoleNames_EmptyOrgList() {
        // Given
        String userGuid = "user-guid-001";
        List<String> emptyOrgGuids = new ArrayList<>();

        when(organizationQueryService.treeOrgGuids(userGuid)).thenReturn(emptyOrgGuids);

        // When
        List<String> result = userRoleService.getRoleNames(userGuid);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("根据ID解绑用户角色 - 正常情况")
    void testUnbindById_Success() {
        // Given
        Long userRoleId = 1L;

        UserRoleServiceImpl spyService = spy(userRoleService);
        doReturn(testUserRole).when(spyService).getById(userRoleId);
        doReturn(true).when(spyService).removeById(userRoleId);

        when(roleService.getById(100L)).thenReturn(testRole);

        // When
        assertDoesNotThrow(() -> spyService.unbindById(userRoleId));

        // Then
        verify(spyService, times(1)).removeById(userRoleId);
        verify(remoteDataLimitRefreshService, times(1)).refresh();
    }

    @Test
    @DisplayName("根据ID获取用户角色详情 - 正常情况")
    void testGetDetailById_Success() {
        // Given
        Long userRoleId = 1L;
        List<UserRole> userRoles = Arrays.asList(testUserRole);

        UserRoleServiceImpl spyService = spy(userRoleService);
        doReturn(testUserRole).when(spyService).getById(userRoleId);
        doReturn(userRoles).when(spyService).listByRoleId(100L);

        when(roleService.getPlatformByRoleId(100L)).thenReturn(1L);

        // When
        UserRoleDetailVo result = spyService.getDetailById(userRoleId);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getPlatformId());
        assertEquals(1, result.getUserRoleItems().size());
        assertEquals("user-guid-001", result.getUserRoleItems().get(0).getAuthorizedGuid());
        assertEquals(Integer.valueOf(0), result.getUserRoleItems().get(0).getType());
    }

    @Test
    @DisplayName("获取数据角色ID列表")
    void testListDataRoleIds() {
        // Given
        String userGuid = "user-guid-001";
        String organizationGuid = "org-guid-001";
        List<Long> expectedRoleIds = Arrays.asList(100L, 101L);

        when(userRoleMapper.listDataRoleIds(userGuid, organizationGuid)).thenReturn(expectedRoleIds);

        // When
        List<Long> result = userRoleService.listDataRoleIds(userGuid, organizationGuid);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(100L, result.get(0));
        assertEquals(101L, result.get(1));
    }

    /**
     * 创建测试用的UserRoleAddVo对象
     */
    private UserRoleAddVo createUserRoleAddVo() {
        UserRoleAddVo userRoleAddVo = new UserRoleAddVo();
        userRoleAddVo.setGuid("user-guid-001");
        userRoleAddVo.setType(0);
        return userRoleAddVo;
    }
}